2025-08-07 16:11:27,917 - __main__ - INFO - Version: 1.0.0
2025-08-07 16:11:27,917 - __main__ - INFO - Environment: development
2025-08-07 16:11:27,918 - __main__ - INFO - Paper Trading: True
2025-08-07 16:11:27,919 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 16:11:27,926 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 16:11:28,029 - src.config.security - INFO - Encryption system initialized
2025-08-07 16:11:28,116 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 16:11:29,569 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 16:11:29,570 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: Database not initialized
2025-08-07 16:11:29,651 - ccxt.base.exchange - WARNING - binance requires to release all resources with an explicit call to the .close() coroutine. If you are using the exchange instance with async coroutines, add `await exchange.close()` to your code into a place when you're done with the exchange and don't need the exchange instance anymore (at the end of your async coroutine).
2025-08-07 16:11:29,652 - asyncio - ERROR - Unclosed client session
client_session: <aiohttp.client.ClientSession object at 0x000002774FA9B910>
2025-08-07 16:11:29,652 - asyncio - ERROR - Unclosed connector
connections: ['[(<aiohttp.client_proto.ResponseHandler object at 0x000002774FD22660>, 270953.953)]']
connector: <aiohttp.connector.TCPConnector object at 0x000002774F7C5310>
2025-08-07 16:13:00,279 - __main__ - INFO - Version: 1.0.0
2025-08-07 16:13:00,279 - __main__ - INFO - Environment: development
2025-08-07 16:13:00,280 - __main__ - INFO - Paper Trading: True
2025-08-07 16:13:00,280 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 16:13:00,282 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 16:13:00,361 - src.config.security - INFO - Encryption system initialized
2025-08-07 16:13:00,440 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 16:13:01,859 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 16:13:01,859 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: Database not initialized
2025-08-07 16:13:01,862 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 16:13:01,862 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 16:13:02,259 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:13:02,328 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 16:13:02,328 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 16:13:02,471 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:13:02,499 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 16:13:02,500 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 16:13:02,507 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 16:13:02,509 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 16:13:02,524 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 16:13:02,524 - src.data.market_data_collector - INFO - Starting collection loop for coinbasepro
2025-08-07 16:13:02,524 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 16:13:02,524 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 16:13:02,913 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:13:02,913 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:13:02,914 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:13:02,914 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:13:03,058 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:03,073 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:13:04,545 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:05,980 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:06,671 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:07,449 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:07,926 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:08,917 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:09,025 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:09,485 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:13:10,120 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:10,385 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:11,358 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:11,847 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:12,603 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:13,311 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:14,509 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:13:14,509 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:13:14,509 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:13:14,509 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:13:14,633 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:14,988 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:15,905 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:13:16,096 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:16,418 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:17,560 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:17,735 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:18,834 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:19,022 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:20,071 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:20,486 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:21,241 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:21,957 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:22,079 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:13:23,431 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:23,552 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:24,760 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:24,893 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:26,090 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:13:26,091 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:13:26,091 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:13:26,091 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:13:26,104 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:26,215 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:27,385 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:27,680 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:28,234 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:13:28,578 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:29,143 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:29,834 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:30,610 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:32,671 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:32,725 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:33,326 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:33,532 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:34,403 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:13:34,436 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:34,994 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:35,720 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:36,457 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:36,957 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:37,653 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:13:37,653 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:13:37,653 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:13:37,653 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:13:37,774 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:38,269 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:39,240 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:40,557 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:13:40,696 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:40,709 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:41,847 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:42,177 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:43,116 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:43,644 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:44,276 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:45,118 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:45,553 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:46,584 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:46,729 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:13:46,794 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:48,051 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:49,137 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:49,251 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:13:49,251 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:13:49,251 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:13:49,252 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:13:49,367 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:50,296 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:50,828 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:51,645 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:52,295 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:52,793 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:52,876 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:13:53,758 - src.exchanges.exchange_manager - ERROR - Failed to get ticker SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:53,959 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:55,056 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:55,224 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:56,688 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:57,406 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:58,152 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:58,568 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:13:59,036 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:13:59,616 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:13:59,908 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:00,816 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:14:00,817 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:14:00,817 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:14:00,817 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:14:00,935 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:01,166 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:02,396 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:02,402 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:03,643 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:03,877 - src.exchanges.exchange_manager - ERROR - Failed to get ticker DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:05,257 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:14:05,341 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:05,978 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:06,796 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:07,244 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:08,260 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:08,518 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:09,726 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:09,809 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:11,055 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:11,190 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:11,414 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:14:12,232 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:12,233 - src.exchanges.exchange_manager - ERROR - Failed to get ticker MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:14:12,233 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:14:12,233 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:14:12,233 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:14:12,233 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:14:12,233 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:14:12,234 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:14:12,397 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:14:12,397 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:14:12,397 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:14:12,398 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:14:12,512 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:13,982 - src.exchanges.exchange_manager - ERROR - Failed to get ticker DOT/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:14,594 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:15,448 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOT/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:15,802 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:16,915 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOT/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:17,028 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:17,572 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:14:18,300 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:18,380 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOT/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:19,520 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:19,843 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOT/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:20,692 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:20,693 - src.exchanges.exchange_manager - ERROR - Failed to get ticker UNI/USDT from kraken: kraken does not have market symbol UNI/USDT
2025-08-07 16:14:20,693 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from kraken: kraken does not have market symbol UNI/USDT
2025-08-07 16:14:20,693 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from kraken: kraken does not have market symbol UNI/USDT
2025-08-07 16:14:20,693 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from kraken: kraken does not have market symbol UNI/USDT
2025-08-07 16:14:20,693 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from kraken: kraken does not have market symbol UNI/USDT
2025-08-07 16:14:20,694 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from kraken: kraken does not have market symbol UNI/USDT
2025-08-07 16:14:20,694 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from kraken: kraken does not have market symbol UNI/USDT
2025-08-07 16:14:21,309 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOT/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:22,773 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOT/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:23,210 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:23,738 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:14:23,974 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:14:23,974 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:14:23,976 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:14:23,976 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:14:24,094 - src.exchanges.exchange_manager - ERROR - Failed to get ticker MATIC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:24,351 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:25,558 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:25,682 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:26,896 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:27,021 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:28,091 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:28,483 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:29,424 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:29,425 - src.exchanges.exchange_manager - ERROR - Failed to get ticker AAVE/USDT from kraken: kraken does not have market symbol AAVE/USDT
2025-08-07 16:14:29,425 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from kraken: kraken does not have market symbol AAVE/USDT
2025-08-07 16:14:29,425 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from kraken: kraken does not have market symbol AAVE/USDT
2025-08-07 16:14:29,425 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from kraken: kraken does not have market symbol AAVE/USDT
2025-08-07 16:14:29,425 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from kraken: kraken does not have market symbol AAVE/USDT
2025-08-07 16:14:29,425 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from kraken: kraken does not have market symbol AAVE/USDT
2025-08-07 16:14:29,426 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from kraken: kraken does not have market symbol AAVE/USDT
2025-08-07 16:14:29,426 - src.exchanges.exchange_manager - ERROR - Failed to get ticker SUSHI/USDT from kraken: kraken does not have market symbol SUSHI/USDT
2025-08-07 16:14:29,426 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SUSHI/USDT from kraken: kraken does not have market symbol SUSHI/USDT
2025-08-07 16:14:29,426 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SUSHI/USDT from kraken: kraken does not have market symbol SUSHI/USDT
2025-08-07 16:14:29,426 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SUSHI/USDT from kraken: kraken does not have market symbol SUSHI/USDT
2025-08-07 16:14:29,427 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SUSHI/USDT from kraken: kraken does not have market symbol SUSHI/USDT
2025-08-07 16:14:29,427 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SUSHI/USDT from kraken: kraken does not have market symbol SUSHI/USDT
2025-08-07 16:14:29,427 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SUSHI/USDT from kraken: kraken does not have market symbol SUSHI/USDT
2025-08-07 16:14:29,427 - src.exchanges.exchange_manager - ERROR - Failed to get ticker COMP/USDT from kraken: kraken does not have market symbol COMP/USDT
2025-08-07 16:14:29,427 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV COMP/USDT from kraken: kraken does not have market symbol COMP/USDT
2025-08-07 16:14:29,427 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV COMP/USDT from kraken: kraken does not have market symbol COMP/USDT
2025-08-07 16:14:29,428 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV COMP/USDT from kraken: kraken does not have market symbol COMP/USDT
2025-08-07 16:14:29,428 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV COMP/USDT from kraken: kraken does not have market symbol COMP/USDT
2025-08-07 16:14:29,428 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV COMP/USDT from kraken: kraken does not have market symbol COMP/USDT
2025-08-07 16:14:29,428 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV COMP/USDT from kraken: kraken does not have market symbol COMP/USDT
2025-08-07 16:14:29,894 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BNB/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:14:29,950 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:31,421 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:31,871 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:32,888 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:33,034 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:34,724 - src.exchanges.exchange_manager - ERROR - Failed to get ticker AVAX/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:34,785 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:35,516 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:35,554 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:14:35,554 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:14:35,554 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:14:35,555 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:14:35,677 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AVAX/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:36,049 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:14:36,791 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:37,139 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AVAX/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:38,012 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:38,602 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AVAX/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:40,069 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AVAX/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:40,424 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:41,536 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AVAX/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:41,766 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:42,207 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:14:42,990 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:43,004 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AVAX/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:44,147 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:44,475 - src.exchanges.exchange_manager - ERROR - Failed to get ticker UNI/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:45,323 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:45,941 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:46,622 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:46,622 - src.exchanges.exchange_manager - ERROR - Failed to get ticker NEAR/USDT from kraken: kraken does not have market symbol NEAR/USDT
2025-08-07 16:14:46,622 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV NEAR/USDT from kraken: kraken does not have market symbol NEAR/USDT
2025-08-07 16:14:46,623 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV NEAR/USDT from kraken: kraken does not have market symbol NEAR/USDT
2025-08-07 16:14:46,623 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV NEAR/USDT from kraken: kraken does not have market symbol NEAR/USDT
2025-08-07 16:14:46,623 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV NEAR/USDT from kraken: kraken does not have market symbol NEAR/USDT
2025-08-07 16:14:46,623 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV NEAR/USDT from kraken: kraken does not have market symbol NEAR/USDT
2025-08-07 16:14:46,623 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV NEAR/USDT from kraken: kraken does not have market symbol NEAR/USDT
2025-08-07 16:14:46,624 - src.exchanges.exchange_manager - ERROR - Failed to get ticker FTM/USDT from kraken: kraken does not have market symbol FTM/USDT
2025-08-07 16:14:46,624 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FTM/USDT from kraken: kraken does not have market symbol FTM/USDT
2025-08-07 16:14:46,624 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FTM/USDT from kraken: kraken does not have market symbol FTM/USDT
2025-08-07 16:14:46,624 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FTM/USDT from kraken: kraken does not have market symbol FTM/USDT
2025-08-07 16:14:46,624 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FTM/USDT from kraken: kraken does not have market symbol FTM/USDT
2025-08-07 16:14:46,624 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FTM/USDT from kraken: kraken does not have market symbol FTM/USDT
2025-08-07 16:14:46,624 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FTM/USDT from kraken: kraken does not have market symbol FTM/USDT
2025-08-07 16:14:46,625 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ONE/USDT from kraken: kraken does not have market symbol ONE/USDT
2025-08-07 16:14:46,625 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ONE/USDT from kraken: kraken does not have market symbol ONE/USDT
2025-08-07 16:14:46,625 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ONE/USDT from kraken: kraken does not have market symbol ONE/USDT
2025-08-07 16:14:46,625 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ONE/USDT from kraken: kraken does not have market symbol ONE/USDT
2025-08-07 16:14:46,625 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ONE/USDT from kraken: kraken does not have market symbol ONE/USDT
2025-08-07 16:14:46,625 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ONE/USDT from kraken: kraken does not have market symbol ONE/USDT
2025-08-07 16:14:46,626 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ONE/USDT from kraken: kraken does not have market symbol ONE/USDT
2025-08-07 16:14:47,141 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:14:47,141 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:14:47,142 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:14:47,142 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:14:47,278 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:48,364 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:14:48,733 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:49,085 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:50,199 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:50,212 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:51,436 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:51,656 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:52,608 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:53,122 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV UNI/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:53,893 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:54,519 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:14:54,586 - src.exchanges.exchange_manager - ERROR - Failed to get ticker LINK/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:55,161 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:56,054 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV LINK/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:57,388 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:57,514 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV LINK/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:58,701 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:14:58,724 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:14:58,724 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:14:58,724 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:14:58,725 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:14:58,842 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV LINK/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:14:59,943 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:15:00,307 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV LINK/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:15:00,673 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:15:01,111 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:15:01,766 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV LINK/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:15:02,346 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:15:03,232 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV LINK/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:15:03,500 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:15:03,501 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETC/USDT from kraken: kraken does not have market symbol ETC/USDT
2025-08-07 16:15:03,501 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETC/USDT from kraken: kraken does not have market symbol ETC/USDT
2025-08-07 16:15:03,501 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETC/USDT from kraken: kraken does not have market symbol ETC/USDT
2025-08-07 16:15:03,501 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETC/USDT from kraken: kraken does not have market symbol ETC/USDT
2025-08-07 16:15:03,501 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETC/USDT from kraken: kraken does not have market symbol ETC/USDT
2025-08-07 16:15:03,502 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETC/USDT from kraken: kraken does not have market symbol ETC/USDT
2025-08-07 16:15:03,502 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETC/USDT from kraken: kraken does not have market symbol ETC/USDT
2025-08-07 16:15:03,502 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XLM/USDT from kraken: kraken does not have market symbol XLM/USDT
2025-08-07 16:15:03,502 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XLM/USDT from kraken: kraken does not have market symbol XLM/USDT
2025-08-07 16:15:03,502 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XLM/USDT from kraken: kraken does not have market symbol XLM/USDT
2025-08-07 16:15:03,502 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XLM/USDT from kraken: kraken does not have market symbol XLM/USDT
2025-08-07 16:15:03,503 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XLM/USDT from kraken: kraken does not have market symbol XLM/USDT
2025-08-07 16:15:03,503 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XLM/USDT from kraken: kraken does not have market symbol XLM/USDT
2025-08-07 16:15:03,503 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XLM/USDT from kraken: kraken does not have market symbol XLM/USDT
2025-08-07 16:15:03,503 - src.exchanges.exchange_manager - ERROR - Failed to get ticker VET/USDT from kraken: kraken does not have market symbol VET/USDT
2025-08-07 16:15:03,503 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV VET/USDT from kraken: kraken does not have market symbol VET/USDT
2025-08-07 16:15:03,503 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV VET/USDT from kraken: kraken does not have market symbol VET/USDT
2025-08-07 16:15:03,503 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV VET/USDT from kraken: kraken does not have market symbol VET/USDT
2025-08-07 16:15:03,504 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV VET/USDT from kraken: kraken does not have market symbol VET/USDT
2025-08-07 16:15:03,504 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV VET/USDT from kraken: kraken does not have market symbol VET/USDT
2025-08-07 16:15:03,504 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV VET/USDT from kraken: kraken does not have market symbol VET/USDT
2025-08-07 16:15:03,504 - src.exchanges.exchange_manager - ERROR - Failed to get ticker THETA/USDT from kraken: kraken does not have market symbol THETA/USDT
2025-08-07 16:15:03,504 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV THETA/USDT from kraken: kraken does not have market symbol THETA/USDT
2025-08-07 16:15:03,504 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV THETA/USDT from kraken: kraken does not have market symbol THETA/USDT
2025-08-07 16:15:03,505 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV THETA/USDT from kraken: kraken does not have market symbol THETA/USDT
2025-08-07 16:15:03,505 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV THETA/USDT from kraken: kraken does not have market symbol THETA/USDT
2025-08-07 16:15:03,505 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV THETA/USDT from kraken: kraken does not have market symbol THETA/USDT
2025-08-07 16:15:03,505 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV THETA/USDT from kraken: kraken does not have market symbol THETA/USDT
2025-08-07 16:15:03,505 - src.exchanges.exchange_manager - ERROR - Failed to get ticker FIL/USDT from kraken: kraken does not have market symbol FIL/USDT
2025-08-07 16:15:03,505 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FIL/USDT from kraken: kraken does not have market symbol FIL/USDT
2025-08-07 16:15:03,506 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FIL/USDT from kraken: kraken does not have market symbol FIL/USDT
2025-08-07 16:15:03,506 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FIL/USDT from kraken: kraken does not have market symbol FIL/USDT
2025-08-07 16:15:03,506 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FIL/USDT from kraken: kraken does not have market symbol FIL/USDT
2025-08-07 16:15:03,506 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FIL/USDT from kraken: kraken does not have market symbol FIL/USDT
2025-08-07 16:15:03,506 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV FIL/USDT from kraken: kraken does not have market symbol FIL/USDT
2025-08-07 16:15:03,507 - src.exchanges.exchange_manager - ERROR - Failed to get ticker TRX/USDT from kraken: kraken does not have market symbol TRX/USDT
2025-08-07 16:15:03,507 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV TRX/USDT from kraken: kraken does not have market symbol TRX/USDT
2025-08-07 16:15:03,507 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV TRX/USDT from kraken: kraken does not have market symbol TRX/USDT
2025-08-07 16:15:03,507 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV TRX/USDT from kraken: kraken does not have market symbol TRX/USDT
2025-08-07 16:15:03,507 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV TRX/USDT from kraken: kraken does not have market symbol TRX/USDT
2025-08-07 16:15:03,507 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV TRX/USDT from kraken: kraken does not have market symbol TRX/USDT
2025-08-07 16:15:03,508 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV TRX/USDT from kraken: kraken does not have market symbol TRX/USDT
2025-08-07 16:15:03,508 - src.exchanges.exchange_manager - ERROR - Failed to get ticker EOS/USDT from kraken: kraken does not have market symbol EOS/USDT
2025-08-07 16:15:03,508 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV EOS/USDT from kraken: kraken does not have market symbol EOS/USDT
2025-08-07 16:15:03,508 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV EOS/USDT from kraken: kraken does not have market symbol EOS/USDT
2025-08-07 16:15:03,508 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV EOS/USDT from kraken: kraken does not have market symbol EOS/USDT
2025-08-07 16:15:03,508 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV EOS/USDT from kraken: kraken does not have market symbol EOS/USDT
2025-08-07 16:15:03,509 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV EOS/USDT from kraken: kraken does not have market symbol EOS/USDT
2025-08-07 16:15:03,509 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV EOS/USDT from kraken: kraken does not have market symbol EOS/USDT
2025-08-07 16:15:03,509 - src.exchanges.exchange_manager - ERROR - Failed to get ticker IOTA/USDT from kraken: kraken does not have market symbol IOTA/USDT
2025-08-07 16:15:03,510 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV IOTA/USDT from kraken: kraken does not have market symbol IOTA/USDT
2025-08-07 16:15:03,510 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV IOTA/USDT from kraken: kraken does not have market symbol IOTA/USDT
2025-08-07 16:15:03,510 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV IOTA/USDT from kraken: kraken does not have market symbol IOTA/USDT
2025-08-07 16:15:03,510 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV IOTA/USDT from kraken: kraken does not have market symbol IOTA/USDT
2025-08-07 16:15:03,510 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV IOTA/USDT from kraken: kraken does not have market symbol IOTA/USDT
2025-08-07 16:15:03,510 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV IOTA/USDT from kraken: kraken does not have market symbol IOTA/USDT
2025-08-07 16:15:05,462 - src.exchanges.exchange_manager - ERROR - Failed to get ticker AAVE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:15:06,913 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:15:06,929 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:15:08,397 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:15:09,858 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:15:11,060 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:15:11,060 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:15:11,061 - src.core.trading_engine - ERROR - Health check error: 'RiskManager' object has no attribute 'check_risk_limits'
2025-08-07 16:15:11,061 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:15:11,184 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:15:12,645 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:15:13,315 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XRP/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:15:14,111 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV AAVE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:25:58,493 - __main__ - INFO - Version: 1.0.0
2025-08-07 16:25:58,493 - __main__ - INFO - Environment: development
2025-08-07 16:25:58,493 - __main__ - INFO - Paper Trading: True
2025-08-07 16:25:58,493 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 16:25:58,497 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 16:25:58,585 - src.config.security - INFO - Encryption system initialized
2025-08-07 16:25:58,669 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 16:26:00,104 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 16:26:00,104 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: Database not initialized
2025-08-07 16:26:00,108 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 16:26:00,108 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 16:26:00,572 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:26:00,609 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 16:26:00,610 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 16:26:00,754 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:26:00,836 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 16:26:00,837 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 16:26:00,845 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 16:26:00,848 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 16:26:00,868 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 16:26:00,869 - src.data.market_data_collector - INFO - Starting collection loop for coinbasepro
2025-08-07 16:26:00,869 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 16:26:00,869 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 16:26:01,259 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:26:01,260 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:26:01,260 - src.risk.risk_manager - ERROR - Error in check_risk_limits: 'RiskManager' object has no attribute 'positions'
2025-08-07 16:26:01,260 - src.risk.risk_manager - CRITICAL - Risk Event: Risk check failed: 'RiskManager' object has no attribute 'positions'
2025-08-07 16:26:01,260 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:26:01,389 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:01,418 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:26:02,859 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:04,326 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:04,795 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:05,788 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:05,906 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:07,014 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:07,252 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:07,575 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:26:08,254 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:08,724 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:09,548 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:10,190 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:10,791 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:11,658 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:12,853 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:26:12,854 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:26:12,854 - src.risk.risk_manager - ERROR - Error in check_risk_limits: 'RiskManager' object has no attribute 'positions'
2025-08-07 16:26:12,854 - src.risk.risk_manager - CRITICAL - Risk Event: Risk check failed: 'RiskManager' object has no attribute 'positions'
2025-08-07 16:26:12,854 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:26:12,983 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:13,092 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:13,730 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:26:14,269 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:14,462 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:15,487 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:15,915 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:16,725 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:17,381 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:17,984 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:18,853 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:19,165 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:20,150 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:26:20,319 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:21,639 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:21,786 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:22,787 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:23,249 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:23,957 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:24,449 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:26:24,449 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:26:24,449 - src.risk.risk_manager - ERROR - Error in check_risk_limits: 'RiskManager' object has no attribute 'positions'
2025-08-07 16:26:24,449 - src.risk.risk_manager - CRITICAL - Risk Event: Risk check failed: 'RiskManager' object has no attribute 'positions'
2025-08-07 16:26:24,449 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:26:24,571 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:25,124 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:26,034 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:26,307 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:26:26,397 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:27,497 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:27,506 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:28,965 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:30,930 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:31,049 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:31,117 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:31,906 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:32,299 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:32,466 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:26:33,371 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:33,476 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:34,759 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:34,831 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:35,928 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:36,053 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:26:36,054 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:26:36,054 - src.risk.risk_manager - ERROR - Error in check_risk_limits: 'RiskManager' object has no attribute 'positions'
2025-08-07 16:26:36,054 - src.risk.risk_manager - CRITICAL - Risk Event: Risk check failed: 'RiskManager' object has no attribute 'positions'
2025-08-07 16:26:36,054 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:26:36,157 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:37,643 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:38,317 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:38,896 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:26:39,099 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:39,526 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:40,562 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:40,806 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:42,008 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:42,083 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:43,292 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:43,549 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:44,402 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:45,020 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:45,311 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:26:46,485 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:46,767 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:26:47,682 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:26:47,682 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:26:47,682 - src.risk.risk_manager - ERROR - Error in check_risk_limits: 'RiskManager' object has no attribute 'positions'
2025-08-07 16:26:47,682 - src.risk.risk_manager - CRITICAL - Risk Event: Risk check failed: 'RiskManager' object has no attribute 'positions'
2025-08-07 16:26:47,682 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:26:47,805 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:26:47,981 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:07,168 - __main__ - INFO - Version: 1.0.0
2025-08-07 16:27:07,168 - __main__ - INFO - Environment: development
2025-08-07 16:27:07,168 - __main__ - INFO - Paper Trading: True
2025-08-07 16:27:07,169 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 16:27:07,172 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 16:27:07,264 - src.config.security - INFO - Encryption system initialized
2025-08-07 16:27:07,347 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 16:27:08,862 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 16:27:08,862 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: Database not initialized
2025-08-07 16:27:08,876 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 16:27:08,876 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 16:27:09,474 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:27:09,507 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 16:27:09,508 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 16:27:09,646 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:27:09,719 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 16:27:09,719 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 16:27:09,728 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 16:27:09,731 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 16:27:09,751 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 16:27:09,752 - src.data.market_data_collector - INFO - Starting collection loop for coinbasepro
2025-08-07 16:27:09,752 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 16:27:09,752 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 16:27:10,031 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:27:10,143 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:27:10,143 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:27:10,143 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:27:10,144 - src.core.trading_engine - ERROR - Health check error: 'PortfolioManager' object has no attribute 'health_check'
2025-08-07 16:27:10,144 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:27:10,285 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:11,749 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:13,210 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:13,733 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:14,680 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:14,897 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:16,043 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:16,148 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:16,193 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:27:17,147 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:17,619 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:18,355 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:19,087 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:19,578 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:20,551 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:21,746 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:27:21,746 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:27:21,747 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:27:21,747 - src.core.trading_engine - ERROR - Health check error: 'PortfolioManager' object has no attribute 'health_check'
2025-08-07 16:27:21,747 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:27:21,875 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:21,929 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:22,344 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:27:23,260 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:23,343 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:24,516 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:24,860 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:25,782 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:26,327 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:27,047 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:27,790 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:28,262 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:28,498 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:27:29,256 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:30,492 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:30,722 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:31,731 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:32,186 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:32,977 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:33,387 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:27:33,387 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:27:33,388 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:27:33,388 - src.core.trading_engine - ERROR - Health check error: 'PortfolioManager' object has no attribute 'health_check'
2025-08-07 16:27:33,388 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:27:33,530 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:34,204 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:34,912 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:27:34,982 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:35,383 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:36,447 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:36,639 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:37,907 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:39,587 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:39,649 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:40,329 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:40,844 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:41,084 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:27:41,590 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:42,308 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:42,800 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:43,773 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:44,121 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:44,968 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:27:44,968 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:27:44,968 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:27:44,968 - src.core.trading_engine - ERROR - Health check error: 'PortfolioManager' object has no attribute 'health_check'
2025-08-07 16:27:44,968 - src.core.trading_engine - WARNING - Health check failed, skipping trading cycle
2025-08-07 16:27:45,094 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:45,300 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:46,556 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:47,234 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:27:47,706 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:48,016 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:49,061 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:49,471 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:50,154 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:27:50,941 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:27:51,331 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:19,827 - __main__ - INFO - Version: 1.0.0
2025-08-07 16:28:19,827 - __main__ - INFO - Environment: development
2025-08-07 16:28:19,827 - __main__ - INFO - Paper Trading: True
2025-08-07 16:28:19,827 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 16:28:19,832 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 16:28:19,938 - src.config.security - INFO - Encryption system initialized
2025-08-07 16:28:20,064 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 16:28:21,624 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 16:28:21,624 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: Database not initialized
2025-08-07 16:28:21,628 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 16:28:21,628 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 16:28:22,080 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:28:22,158 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 16:28:22,158 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 16:28:22,297 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:28:22,379 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 16:28:22,379 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 16:28:22,390 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 16:28:22,390 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 16:28:22,411 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 16:28:22,411 - src.data.market_data_collector - INFO - Starting collection loop for coinbasepro
2025-08-07 16:28:22,411 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 16:28:22,411 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 16:28:22,673 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:28:22,808 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:28:22,808 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:28:22,808 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:28:22,808 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:28:22,808 - src.core.trading_engine - ERROR - Analysis error: 'TechnicalAnalyzer' object has no attribute 'analyze'
2025-08-07 16:28:22,808 - src.core.trading_engine - ERROR - Error in main trading loop: 'AITradingAgent' object has no attribute 'make_decisions'
2025-08-07 16:28:22,937 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:24,393 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:25,861 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:26,138 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:27,326 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:27,386 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:28,488 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:28,786 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:28,847 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:28:29,615 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:29,986 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:28:29,986 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:28:29,986 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:28:29,986 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:28:29,986 - src.core.trading_engine - ERROR - Analysis error: 'TechnicalAnalyzer' object has no attribute 'analyze'
2025-08-07 16:28:29,986 - src.core.trading_engine - ERROR - Error in main trading loop: 'AITradingAgent' object has no attribute 'make_decisions'
2025-08-07 16:28:30,106 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:30,722 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:31,577 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:31,903 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:33,042 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:34,323 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:34,508 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:35,273 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:28:35,676 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:35,983 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:36,833 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:37,177 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:28:37,178 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:28:37,178 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:28:37,178 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:28:37,178 - src.core.trading_engine - ERROR - Analysis error: 'TechnicalAnalyzer' object has no attribute 'analyze'
2025-08-07 16:28:37,178 - src.core.trading_engine - ERROR - Error in main trading loop: 'AITradingAgent' object has no attribute 'make_decisions'
2025-08-07 16:28:37,300 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:38,114 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:38,759 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:39,293 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:40,223 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:40,556 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:41,475 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:28:41,696 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:42,950 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:43,157 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:44,173 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:44,366 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:28:44,366 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:28:44,366 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:28:44,366 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:28:44,367 - src.core.trading_engine - ERROR - Analysis error: 'TechnicalAnalyzer' object has no attribute 'analyze'
2025-08-07 16:28:44,367 - src.core.trading_engine - ERROR - Error in main trading loop: 'AITradingAgent' object has no attribute 'make_decisions'
2025-08-07 16:28:44,486 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:45,325 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:45,951 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:46,501 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:47,418 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:47,647 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:28:47,798 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:48,886 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:48,889 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:50,350 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:52,196 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:52,357 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:28:52,361 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:28:52,363 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:28:52,369 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:28:52,371 - src.core.trading_engine - ERROR - Analysis error: 'TechnicalAnalyzer' object has no attribute 'analyze'
2025-08-07 16:28:52,380 - src.core.trading_engine - ERROR - Error in main trading loop: 'AITradingAgent' object has no attribute 'make_decisions'
2025-08-07 16:28:52,381 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:52,447 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:53,163 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:53,623 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:53,805 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:28:54,631 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:54,846 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:56,091 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:56,094 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:57,313 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:57,568 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:58,837 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:28:58,837 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:28:58,838 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:28:58,838 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:28:58,838 - src.core.trading_engine - ERROR - Analysis error: 'TechnicalAnalyzer' object has no attribute 'analyze'
2025-08-07 16:28:58,838 - src.core.trading_engine - ERROR - Error in main trading loop: 'AITradingAgent' object has no attribute 'make_decisions'
2025-08-07 16:28:58,962 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:28:59,668 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:28:59,962 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:29:00,430 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:29:00,923 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:29:01,898 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:29:02,195 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:29:03,367 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:29:03,414 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:29:04,739 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:29:04,833 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:29:05,981 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:29:06,028 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:29:06,028 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:29:06,029 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:29:06,029 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:29:06,029 - src.core.trading_engine - ERROR - Analysis error: 'TechnicalAnalyzer' object has no attribute 'analyze'
2025-08-07 16:29:06,029 - src.core.trading_engine - ERROR - Error in main trading loop: 'AITradingAgent' object has no attribute 'make_decisions'
2025-08-07 16:29:06,127 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:29:06,155 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:29:07,617 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:29:08,430 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:29:09,091 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:29:09,698 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:29:10,557 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:29:10,904 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:34,972 - __main__ - INFO - Version: 1.0.0
2025-08-07 16:30:34,972 - __main__ - INFO - Environment: development
2025-08-07 16:30:34,972 - __main__ - INFO - Paper Trading: True
2025-08-07 16:30:34,972 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 16:30:34,975 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 16:30:35,059 - src.config.security - INFO - Encryption system initialized
2025-08-07 16:30:35,149 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 16:30:36,566 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 16:30:36,566 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: Database not initialized
2025-08-07 16:30:36,569 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 16:30:36,569 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 16:30:37,006 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:30:37,078 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 16:30:37,079 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 16:30:37,212 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:30:37,289 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 16:30:37,289 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 16:30:37,296 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 16:30:37,298 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 16:30:37,312 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 16:30:37,312 - src.data.market_data_collector - INFO - Starting collection loop for coinbasepro
2025-08-07 16:30:37,312 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 16:30:37,313 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 16:30:37,701 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:30:37,701 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:30:37,701 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:30:37,702 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:30:37,702 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {}, 'stats': {'total_updates': 0, 'successful_updates': 0, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 0, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 0, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 37, 702261)}, returning basic analysis
2025-08-07 16:30:37,702 - src.core.trading_engine - ERROR - Analysis error: 'SentimentAnalyzer' object has no attribute 'get_latest_sentiment'
2025-08-07 16:30:37,703 - src.core.ai_agent - ERROR - Error in make_decisions: 'AITradingAgent' object has no attribute 'analyze_and_decide'
2025-08-07 16:30:37,703 - src.core.trading_engine - ERROR - Trade execution error: RiskManager.validate_trade() missing 4 required positional arguments: 'side', 'quantity', 'price', and 'portfolio_value'
2025-08-07 16:30:37,703 - src.core.trading_engine - ERROR - Metrics update error: 'PortfolioManager' object has no attribute 'get_total_value'
2025-08-07 16:30:37,829 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:30:37,835 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:39,293 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:40,488 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:30:40,488 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:30:40,489 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:30:40,489 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:30:40,489 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {}, 'stats': {'total_updates': 0, 'successful_updates': 0, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 0, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 0, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 40, 489898)}, returning basic analysis
2025-08-07 16:30:40,489 - src.core.trading_engine - ERROR - Analysis error: 'SentimentAnalyzer' object has no attribute 'get_latest_sentiment'
2025-08-07 16:30:40,489 - src.core.ai_agent - ERROR - Error in make_decisions: 'AITradingAgent' object has no attribute 'analyze_and_decide'
2025-08-07 16:30:40,490 - src.core.trading_engine - ERROR - Trade execution error: RiskManager.validate_trade() missing 4 required positional arguments: 'side', 'quantity', 'price', and 'portfolio_value'
2025-08-07 16:30:40,490 - src.core.trading_engine - ERROR - Metrics update error: 'PortfolioManager' object has no attribute 'get_total_value'
2025-08-07 16:30:40,614 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:41,298 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:42,080 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:42,559 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:43,273 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:30:43,274 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:30:43,274 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:30:43,274 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:30:43,274 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {}, 'stats': {'total_updates': 0, 'successful_updates': 0, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 0, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 0, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 43, 274821)}, returning basic analysis
2025-08-07 16:30:43,275 - src.core.trading_engine - ERROR - Analysis error: 'SentimentAnalyzer' object has no attribute 'get_latest_sentiment'
2025-08-07 16:30:43,275 - src.core.ai_agent - ERROR - Error in make_decisions: 'AITradingAgent' object has no attribute 'analyze_and_decide'
2025-08-07 16:30:43,275 - src.core.trading_engine - ERROR - Trade execution error: RiskManager.validate_trade() missing 4 required positional arguments: 'side', 'quantity', 'price', and 'portfolio_value'
2025-08-07 16:30:43,275 - src.core.trading_engine - ERROR - Metrics update error: 'PortfolioManager' object has no attribute 'get_total_value'
2025-08-07 16:30:43,401 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:43,775 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:43,995 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:30:44,864 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:45,055 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:46,061 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:30:46,061 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:30:46,061 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:30:46,062 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:30:46,062 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {}, 'stats': {'total_updates': 0, 'successful_updates': 0, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 0, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 0, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 46, 62486)}, returning basic analysis
2025-08-07 16:30:46,062 - src.core.trading_engine - ERROR - Analysis error: 'SentimentAnalyzer' object has no attribute 'get_latest_sentiment'
2025-08-07 16:30:46,062 - src.core.ai_agent - ERROR - Error in make_decisions: 'AITradingAgent' object has no attribute 'analyze_and_decide'
2025-08-07 16:30:46,062 - src.core.trading_engine - ERROR - Trade execution error: RiskManager.validate_trade() missing 4 required positional arguments: 'side', 'quantity', 'price', and 'portfolio_value'
2025-08-07 16:30:46,063 - src.core.trading_engine - ERROR - Metrics update error: 'PortfolioManager' object has no attribute 'get_total_value'
2025-08-07 16:30:46,151 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:46,187 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:47,432 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:47,656 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:48,861 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:30:48,862 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:30:48,862 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:30:48,862 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:30:48,862 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 46, 187328), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573440187, 'datetime': '2025-08-07T13:30:40.187Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116396.7, 'bidVolume': None, 'ask': 116416.4, 'askVolume': None, 'vwap': 115445.98721, 'open': 115003.4, 'close': 116426.5, 'last': 116426.5, 'previousClose': None, 'change': 1423.1, 'percentage': 1.2374416756374158, 'average': 115714.95, 'baseVolume': 48.9279885, 'quoteVolume': 5648539.934582027, 'info': {'a': ['116416.40000', '1', '1.000'], 'b': ['116396.70000', '1', '1.000'], 'c': ['116426.50000', '0.02438941'], 'v': ['26.27255038', '48.92798850'], 'p': ['115687.56864', '115445.98721'], 't': ['1245', '2402'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 47, 432090), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 2, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 1, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 1, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 48, 862098)}, returning basic analysis
2025-08-07 16:30:48,863 - src.core.trading_engine - ERROR - Analysis error: 'SentimentAnalyzer' object has no attribute 'get_latest_sentiment'
2025-08-07 16:30:48,863 - src.core.ai_agent - ERROR - Error in make_decisions: 'AITradingAgent' object has no attribute 'analyze_and_decide'
2025-08-07 16:30:48,863 - src.core.trading_engine - ERROR - Trade execution error: RiskManager.validate_trade() missing 4 required positional arguments: 'side', 'quantity', 'price', and 'portfolio_value'
2025-08-07 16:30:48,863 - src.core.trading_engine - ERROR - Metrics update error: 'PortfolioManager' object has no attribute 'get_total_value'
2025-08-07 16:30:48,972 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:49,734 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:50,162 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:30:50,439 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:50,842 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:51,643 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:30:51,644 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:30:51,644 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:30:51,644 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:30:51,644 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 46, 187328), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573440187, 'datetime': '2025-08-07T13:30:40.187Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116396.7, 'bidVolume': None, 'ask': 116416.4, 'askVolume': None, 'vwap': 115445.98721, 'open': 115003.4, 'close': 116426.5, 'last': 116426.5, 'previousClose': None, 'change': 1423.1, 'percentage': 1.2374416756374158, 'average': 115714.95, 'baseVolume': 48.9279885, 'quoteVolume': 5648539.934582027, 'info': {'a': ['116416.40000', '1', '1.000'], 'b': ['116396.70000', '1', '1.000'], 'c': ['116426.50000', '0.02438941'], 'v': ['26.27255038', '48.92798850'], 'p': ['115687.56864', '115445.98721'], 't': ['1245', '2402'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 47, 432090), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 2, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 1, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 1, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 51, 644072)}, returning basic analysis
2025-08-07 16:30:51,644 - src.core.trading_engine - ERROR - Analysis error: 'SentimentAnalyzer' object has no attribute 'get_latest_sentiment'
2025-08-07 16:30:51,645 - src.core.ai_agent - ERROR - Error in make_decisions: 'AITradingAgent' object has no attribute 'analyze_and_decide'
2025-08-07 16:30:51,645 - src.core.trading_engine - ERROR - Trade execution error: RiskManager.validate_trade() missing 4 required positional arguments: 'side', 'quantity', 'price', and 'portfolio_value'
2025-08-07 16:30:51,645 - src.core.trading_engine - ERROR - Metrics update error: 'PortfolioManager' object has no attribute 'get_total_value'
2025-08-07 16:30:51,761 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:52,017 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:53,226 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:53,267 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:54,429 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:30:54,430 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:30:54,430 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:30:54,430 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:30:54,430 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 46, 187328), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573440187, 'datetime': '2025-08-07T13:30:40.187Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116396.7, 'bidVolume': None, 'ask': 116416.4, 'askVolume': None, 'vwap': 115445.98721, 'open': 115003.4, 'close': 116426.5, 'last': 116426.5, 'previousClose': None, 'change': 1423.1, 'percentage': 1.2374416756374158, 'average': 115714.95, 'baseVolume': 48.9279885, 'quoteVolume': 5648539.934582027, 'info': {'a': ['116416.40000', '1', '1.000'], 'b': ['116396.70000', '1', '1.000'], 'c': ['116426.50000', '0.02438941'], 'v': ['26.27255038', '48.92798850'], 'p': ['115687.56864', '115445.98721'], 't': ['1245', '2402'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 47, 432090), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 2, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 1, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 1, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 54, 430901)}, returning basic analysis
2025-08-07 16:30:54,431 - src.core.trading_engine - ERROR - Analysis error: 'SentimentAnalyzer' object has no attribute 'get_latest_sentiment'
2025-08-07 16:30:54,431 - src.core.ai_agent - ERROR - Error in make_decisions: 'AITradingAgent' object has no attribute 'analyze_and_decide'
2025-08-07 16:30:54,431 - src.core.trading_engine - ERROR - Trade execution error: RiskManager.validate_trade() missing 4 required positional arguments: 'side', 'quantity', 'price', and 'portfolio_value'
2025-08-07 16:30:54,431 - src.core.trading_engine - ERROR - Metrics update error: 'PortfolioManager' object has no attribute 'get_total_value'
2025-08-07 16:30:54,550 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:54,552 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:55,716 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:56,028 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:56,321 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:30:57,217 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:30:57,217 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:30:57,217 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:30:57,217 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:30:57,218 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 46, 187328), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573440187, 'datetime': '2025-08-07T13:30:40.187Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116396.7, 'bidVolume': None, 'ask': 116416.4, 'askVolume': None, 'vwap': 115445.98721, 'open': 115003.4, 'close': 116426.5, 'last': 116426.5, 'previousClose': None, 'change': 1423.1, 'percentage': 1.2374416756374158, 'average': 115714.95, 'baseVolume': 48.9279885, 'quoteVolume': 5648539.934582027, 'info': {'a': ['116416.40000', '1', '1.000'], 'b': ['116396.70000', '1', '1.000'], 'c': ['116426.50000', '0.02438941'], 'v': ['26.27255038', '48.92798850'], 'p': ['115687.56864', '115445.98721'], 't': ['1245', '2402'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 47, 432090), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573448642, 'datetime': '2025-08-07T13:30:48.642Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3832.6, 'bidVolume': None, 'ask': 3832.61, 'askVolume': None, 'vwap': 3711.29274, 'open': 3682.64, 'close': 3830.52, 'last': 3830.52, 'previousClose': None, 'change': 147.88, 'percentage': 4.015597506136902, 'average': 3756.58, 'baseVolume': 1992.08699558, 'quoteVolume': 7393218.0041444665, 'info': {'a': ['3832.61000', '1', '1.000'], 'b': ['3832.60000', '3', '3.000'], 'c': ['3830.52000', '0.05944340'], 'v': ['988.19766310', '1992.08699558'], 'p': ['3773.42244', '3711.29274'], 't': ['1290', '2244'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 55, 717462), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 56, 28331), 'exchange': 'binance', 'symbol': 'ETH/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 4, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 2, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 2, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 57, 218947)}, returning basic analysis
2025-08-07 16:30:57,218 - src.core.trading_engine - ERROR - Analysis error: 'SentimentAnalyzer' object has no attribute 'get_latest_sentiment'
2025-08-07 16:30:57,218 - src.core.ai_agent - ERROR - Error in make_decisions: 'AITradingAgent' object has no attribute 'analyze_and_decide'
2025-08-07 16:30:57,218 - src.core.trading_engine - ERROR - Trade execution error: RiskManager.validate_trade() missing 4 required positional arguments: 'side', 'quantity', 'price', and 'portfolio_value'
2025-08-07 16:30:57,219 - src.core.trading_engine - ERROR - Metrics update error: 'PortfolioManager' object has no attribute 'get_total_value'
2025-08-07 16:30:57,351 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:58,375 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:30:58,805 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:30:59,535 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:31:00,008 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:31:00,008 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:31:00,008 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:31:00,008 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:31:00,009 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 46, 187328), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573440187, 'datetime': '2025-08-07T13:30:40.187Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116396.7, 'bidVolume': None, 'ask': 116416.4, 'askVolume': None, 'vwap': 115445.98721, 'open': 115003.4, 'close': 116426.5, 'last': 116426.5, 'previousClose': None, 'change': 1423.1, 'percentage': 1.2374416756374158, 'average': 115714.95, 'baseVolume': 48.9279885, 'quoteVolume': 5648539.934582027, 'info': {'a': ['116416.40000', '1', '1.000'], 'b': ['116396.70000', '1', '1.000'], 'c': ['116426.50000', '0.02438941'], 'v': ['26.27255038', '48.92798850'], 'p': ['115687.56864', '115445.98721'], 't': ['1245', '2402'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 47, 432090), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573448642, 'datetime': '2025-08-07T13:30:48.642Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3832.6, 'bidVolume': None, 'ask': 3832.61, 'askVolume': None, 'vwap': 3711.29274, 'open': 3682.64, 'close': 3830.52, 'last': 3830.52, 'previousClose': None, 'change': 147.88, 'percentage': 4.015597506136902, 'average': 3756.58, 'baseVolume': 1992.08699558, 'quoteVolume': 7393218.0041444665, 'info': {'a': ['3832.61000', '1', '1.000'], 'b': ['3832.60000', '3', '3.000'], 'c': ['3830.52000', '0.05944340'], 'v': ['988.19766310', '1992.08699558'], 'p': ['3773.42244', '3711.29274'], 't': ['1290', '2244'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 55, 717462), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 56, 28331), 'exchange': 'binance', 'symbol': 'ETH/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 4, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 2, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 2, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 31, 0, 9499)}, returning basic analysis
2025-08-07 16:31:00,009 - src.core.trading_engine - ERROR - Analysis error: 'SentimentAnalyzer' object has no attribute 'get_latest_sentiment'
2025-08-07 16:31:00,009 - src.core.ai_agent - ERROR - Error in make_decisions: 'AITradingAgent' object has no attribute 'analyze_and_decide'
2025-08-07 16:31:00,010 - src.core.trading_engine - ERROR - Trade execution error: RiskManager.validate_trade() missing 4 required positional arguments: 'side', 'quantity', 'price', and 'portfolio_value'
2025-08-07 16:31:00,010 - src.core.trading_engine - ERROR - Metrics update error: 'PortfolioManager' object has no attribute 'get_total_value'
2025-08-07 16:31:00,128 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:31:00,700 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:31:01,652 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:31:01,870 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:31:02,734 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:31:02,792 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:31:02,792 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:31:02,792 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:31:02,793 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:31:02,793 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 46, 187328), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573440187, 'datetime': '2025-08-07T13:30:40.187Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116396.7, 'bidVolume': None, 'ask': 116416.4, 'askVolume': None, 'vwap': 115445.98721, 'open': 115003.4, 'close': 116426.5, 'last': 116426.5, 'previousClose': None, 'change': 1423.1, 'percentage': 1.2374416756374158, 'average': 115714.95, 'baseVolume': 48.9279885, 'quoteVolume': 5648539.934582027, 'info': {'a': ['116416.40000', '1', '1.000'], 'b': ['116396.70000', '1', '1.000'], 'c': ['116426.50000', '0.02438941'], 'v': ['26.27255038', '48.92798850'], 'p': ['115687.56864', '115445.98721'], 't': ['1245', '2402'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 47, 432090), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573448642, 'datetime': '2025-08-07T13:30:48.642Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3832.6, 'bidVolume': None, 'ask': 3832.61, 'askVolume': None, 'vwap': 3711.29274, 'open': 3682.64, 'close': 3830.52, 'last': 3830.52, 'previousClose': None, 'change': 147.88, 'percentage': 4.015597506136902, 'average': 3756.58, 'baseVolume': 1992.08699558, 'quoteVolume': 7393218.0041444665, 'info': {'a': ['3832.61000', '1', '1.000'], 'b': ['3832.60000', '3', '3.000'], 'c': ['3830.52000', '0.05944340'], 'v': ['988.19766310', '1992.08699558'], 'p': ['3773.42244', '3711.29274'], 't': ['1290', '2244'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 55, 717462), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 56, 28331), 'exchange': 'binance', 'symbol': 'ETH/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 4, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 2, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 2, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 31, 2, 793678)}, returning basic analysis
2025-08-07 16:31:02,793 - src.core.trading_engine - ERROR - Analysis error: 'SentimentAnalyzer' object has no attribute 'get_latest_sentiment'
2025-08-07 16:31:02,793 - src.core.ai_agent - ERROR - Error in make_decisions: 'AITradingAgent' object has no attribute 'analyze_and_decide'
2025-08-07 16:31:02,794 - src.core.trading_engine - ERROR - Trade execution error: RiskManager.validate_trade() missing 4 required positional arguments: 'side', 'quantity', 'price', and 'portfolio_value'
2025-08-07 16:31:02,794 - src.core.trading_engine - ERROR - Metrics update error: 'PortfolioManager' object has no attribute 'get_total_value'
2025-08-07 16:31:02,911 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:31:03,059 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:31:04,165 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:31:04,380 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:31:05,579 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:31:05,580 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:31:05,580 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:31:05,580 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:31:05,581 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 46, 187328), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573440187, 'datetime': '2025-08-07T13:30:40.187Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116396.7, 'bidVolume': None, 'ask': 116416.4, 'askVolume': None, 'vwap': 115445.98721, 'open': 115003.4, 'close': 116426.5, 'last': 116426.5, 'previousClose': None, 'change': 1423.1, 'percentage': 1.2374416756374158, 'average': 115714.95, 'baseVolume': 48.9279885, 'quoteVolume': 5648539.934582027, 'info': {'a': ['116416.40000', '1', '1.000'], 'b': ['116396.70000', '1', '1.000'], 'c': ['116426.50000', '0.02438941'], 'v': ['26.27255038', '48.92798850'], 'p': ['115687.56864', '115445.98721'], 't': ['1245', '2402'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 47, 432090), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573448642, 'datetime': '2025-08-07T13:30:48.642Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3832.6, 'bidVolume': None, 'ask': 3832.61, 'askVolume': None, 'vwap': 3711.29274, 'open': 3682.64, 'close': 3830.52, 'last': 3830.52, 'previousClose': None, 'change': 147.88, 'percentage': 4.015597506136902, 'average': 3756.58, 'baseVolume': 1992.08699558, 'quoteVolume': 7393218.0041444665, 'info': {'a': ['3832.61000', '1', '1.000'], 'b': ['3832.60000', '3', '3.000'], 'c': ['3830.52000', '0.05944340'], 'v': ['988.19766310', '1992.08699558'], 'p': ['3773.42244', '3711.29274'], 't': ['1290', '2244'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 55, 717462), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 30, 56, 28331), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573457103, 'datetime': '2025-08-07T13:30:57.103Z', 'high': 781.07, 'low': 760.07, 'bid': 775.45, 'bidVolume': None, 'ask': 778.15, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.15000', '1', '1.000'], 'b': ['775.45000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 31, 4, 165441), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 5, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 2, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 3, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 31, 5, 580584)}, returning basic analysis
2025-08-07 16:31:05,581 - src.core.trading_engine - ERROR - Analysis error: 'SentimentAnalyzer' object has no attribute 'get_latest_sentiment'
2025-08-07 16:31:05,581 - src.core.ai_agent - ERROR - Error in make_decisions: 'AITradingAgent' object has no attribute 'analyze_and_decide'
2025-08-07 16:31:05,581 - src.core.trading_engine - ERROR - Trade execution error: RiskManager.validate_trade() missing 4 required positional arguments: 'side', 'quantity', 'price', and 'portfolio_value'
2025-08-07 16:31:05,582 - src.core.trading_engine - ERROR - Metrics update error: 'PortfolioManager' object has no attribute 'get_total_value'
2025-08-07 16:31:05,711 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:31:07,263 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:31:07,433 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:31:07,694 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:11,788 - __main__ - INFO - Version: 1.0.0
2025-08-07 16:34:11,788 - __main__ - INFO - Environment: development
2025-08-07 16:34:11,789 - __main__ - INFO - Paper Trading: True
2025-08-07 16:34:11,789 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 16:34:11,793 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 16:34:11,885 - src.config.security - INFO - Encryption system initialized
2025-08-07 16:34:11,975 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 16:34:13,431 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 16:34:13,432 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: Database not initialized
2025-08-07 16:34:13,436 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 16:34:13,436 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 16:34:13,990 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:34:14,066 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 16:34:14,066 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 16:34:14,183 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:34:14,270 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 16:34:14,271 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 16:34:14,280 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 16:34:14,285 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 16:34:14,308 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 16:34:14,308 - src.data.market_data_collector - INFO - Starting collection loop for coinbasepro
2025-08-07 16:34:14,308 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 16:34:14,308 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 16:34:14,701 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:14,701 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:14,702 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:14,702 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:14,702 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {}, 'stats': {'total_updates': 0, 'successful_updates': 0, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 0, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 0, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 14, 702598)}, returning basic analysis
2025-08-07 16:34:14,702 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:14,702 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:14,703 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:14,703 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:14,703 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:14,703 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:14,703 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:14,840 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:14,857 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:34:16,425 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:17,489 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:17,490 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:17,490 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:17,490 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:17,490 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {}, 'stats': {'total_updates': 0, 'successful_updates': 0, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 0, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 0, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 17, 490093)}, returning basic analysis
2025-08-07 16:34:17,490 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:17,490 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:17,490 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:17,490 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:17,491 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:17,491 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:17,491 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:17,613 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:18,547 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:19,078 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:19,716 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:20,271 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:20,272 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:20,272 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:20,272 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:20,272 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {}, 'stats': {'total_updates': 0, 'successful_updates': 0, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 0, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 0, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 20, 272803)}, returning basic analysis
2025-08-07 16:34:20,273 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:20,273 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:20,273 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:20,273 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:20,273 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:20,273 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:20,274 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:20,400 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:20,873 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:21,016 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:34:21,869 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:22,235 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:23,060 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:23,061 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:23,061 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:23,061 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:23,062 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {}, 'stats': {'total_updates': 0, 'successful_updates': 0, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 0, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 0, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 61358)}, returning basic analysis
2025-08-07 16:34:23,062 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:23,062 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:23,062 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:23,062 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:23,062 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:23,063 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:23,063 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:23,185 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:23,437 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:24,651 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:24,768 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:25,847 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:25,847 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:25,848 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:25,848 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:25,848 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 2, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 1, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 1, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 25, 848107)}, returning basic analysis
2025-08-07 16:34:25,848 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:25,848 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:25,848 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:25,848 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:25,848 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:25,848 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:25,849 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:26,467 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:27,114 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:27,436 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:34:27,925 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:28,388 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:29,120 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:29,120 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:29,120 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:29,120 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:29,121 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 2, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 1, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 1, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 29, 121662)}, returning basic analysis
2025-08-07 16:34:29,121 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:29,121 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:29,121 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:29,122 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:29,122 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:29,122 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:29,122 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:29,245 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:29,558 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:30,733 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:30,784 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:31,908 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:31,909 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:31,909 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:31,909 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:31,910 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 2, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 1, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 1, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 31, 910297)}, returning basic analysis
2025-08-07 16:34:31,911 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:31,911 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:31,911 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:31,912 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:31,912 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:31,912 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:31,912 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:32,023 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:32,029 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:33,251 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:33,503 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:33,860 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:34:34,689 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:34,689 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:34,689 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:34,689 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:34,689 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 4, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 2, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 2, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 34, 689993)}, returning basic analysis
2025-08-07 16:34:34,691 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:34,691 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:34,691 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:34,691 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:34,692 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:34,692 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:34,692 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:34,826 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:35,526 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:36,279 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:36,745 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:37,480 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:37,480 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:37,480 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:37,481 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:37,481 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 4, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 2, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 2, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 37, 481883)}, returning basic analysis
2025-08-07 16:34:37,482 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:37,482 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:37,483 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:37,483 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:37,484 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:37,484 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:37,484 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:37,600 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:38,065 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:39,069 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:39,423 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:40,024 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:34:40,260 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:40,260 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:40,260 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:40,261 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:40,261 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 4, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 2, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 2, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 40, 261138)}, returning basic analysis
2025-08-07 16:34:40,261 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:40,261 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:40,261 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:40,261 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:40,261 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:40,261 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:40,261 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:40,384 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:40,711 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:41,848 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:41,865 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:44,008 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:44,008 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:44,009 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:44,009 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:44,009 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573674426, 'datetime': '2025-08-07T13:34:34.426Z', 'high': 781.07, 'low': 760.07, 'bid': 775.5, 'bidVolume': None, 'ask': 778.1, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.10000', '1', '1.000'], 'b': ['775.50000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 41, 865077), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 5, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 2, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 3, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 44, 9831)}, returning basic analysis
2025-08-07 16:34:44,010 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:44,010 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:44,010 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:44,011 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:44,011 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:44,011 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:44,011 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:44,011 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:44,272 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:45,465 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:45,541 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:46,207 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:34:46,682 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:46,682 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:46,682 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:46,684 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:46,686 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573674426, 'datetime': '2025-08-07T13:34:34.426Z', 'high': 781.07, 'low': 760.07, 'bid': 775.5, 'bidVolume': None, 'ask': 778.1, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.10000', '1', '1.000'], 'b': ['775.50000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 41, 865077), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}, 'binance:BNB/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 44, 13037), 'exchange': 'binance', 'symbol': 'BNB/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 6, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 3, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 3, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 46, 686185)}, returning basic analysis
2025-08-07 16:34:46,686 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:46,686 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:46,686 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:46,698 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:46,707 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:46,708 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:46,708 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:46,714 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:46,785 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:47,932 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:48,266 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:49,091 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:49,451 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:49,451 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:49,453 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:49,453 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:49,453 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573674426, 'datetime': '2025-08-07T13:34:34.426Z', 'high': 781.07, 'low': 760.07, 'bid': 775.5, 'bidVolume': None, 'ask': 778.1, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.10000', '1', '1.000'], 'b': ['775.50000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 41, 865077), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}, 'binance:BNB/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 44, 13037), 'exchange': 'binance', 'symbol': 'BNB/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 6, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 3, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 3, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 49, 453335)}, returning basic analysis
2025-08-07 16:34:49,454 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:49,454 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:49,454 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:49,454 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:49,454 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:49,454 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:49,454 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:49,575 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:50,256 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:51,041 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:52,235 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:52,236 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:52,236 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:52,236 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:52,237 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573674426, 'datetime': '2025-08-07T13:34:34.426Z', 'high': 781.07, 'low': 760.07, 'bid': 775.5, 'bidVolume': None, 'ask': 778.1, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.10000', '1', '1.000'], 'b': ['775.50000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 41, 865077), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}, 'binance:BNB/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 44, 13037), 'exchange': 'binance', 'symbol': 'BNB/USDT'}, 'kraken:XRP/USDT': {'ticker': {'symbol': 'XRP/USDT', 'timestamp': 1754573682959, 'datetime': '2025-08-07T13:34:42.959Z', 'high': 3.10334, 'low': 2.93504, 'bid': 3.07459, 'bidVolume': None, 'ask': 3.0746, 'askVolume': None, 'vwap': 3.02490947, 'open': 2.98332, 'close': 3.07069, 'last': 3.07069, 'previousClose': None, 'change': 0.08737, 'percentage': 2.9286164407438693, 'average': 3.027005, 'baseVolume': 1012487.20808822, 'quoteVolume': 3062682.1439999174, 'info': {'a': ['3.07460000', '68', '68.000'], 'b': ['3.07459000', '127', '127.000'], 'c': ['3.07069000', '8.96631050'], 'v': ['640727.79090313', '1012487.20808822'], 'p': ['3.05635373', '3.02490947'], 't': ['1110', '1988'], 'l': ['2.96751000', '2.93504000'], 'h': ['3.10334000', '3.10334000'], 'o': '2.98332000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 50, 257982), 'exchange': 'kraken', 'symbol': 'XRP/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 7, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 3, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 0, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 4, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 52, 236197)}, returning basic analysis
2025-08-07 16:34:52,237 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:52,238 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:52,238 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:52,238 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:52,238 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:52,239 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:52,239 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:52,350 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:34:52,362 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:52,549 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:53,705 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:53,829 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:54,968 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:55,031 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:55,032 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:55,032 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:55,032 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:55,032 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573674426, 'datetime': '2025-08-07T13:34:34.426Z', 'high': 781.07, 'low': 760.07, 'bid': 775.5, 'bidVolume': None, 'ask': 778.1, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.10000', '1', '1.000'], 'b': ['775.50000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 41, 865077), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}, 'binance:BNB/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 44, 13037), 'exchange': 'binance', 'symbol': 'BNB/USDT'}, 'kraken:XRP/USDT': {'ticker': {'symbol': 'XRP/USDT', 'timestamp': 1754573682959, 'datetime': '2025-08-07T13:34:42.959Z', 'high': 3.10334, 'low': 2.93504, 'bid': 3.07459, 'bidVolume': None, 'ask': 3.0746, 'askVolume': None, 'vwap': 3.02490947, 'open': 2.98332, 'close': 3.07069, 'last': 3.07069, 'previousClose': None, 'change': 0.08737, 'percentage': 2.9286164407438693, 'average': 3.027005, 'baseVolume': 1012487.20808822, 'quoteVolume': 3062682.1439999174, 'info': {'a': ['3.07460000', '68', '68.000'], 'b': ['3.07459000', '127', '127.000'], 'c': ['3.07069000', '8.96631050'], 'v': ['640727.79090313', '1012487.20808822'], 'p': ['3.05635373', '3.02490947'], 't': ['1110', '1988'], 'l': ['2.96751000', '2.93504000'], 'h': ['3.10334000', '3.10334000'], 'o': '2.98332000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 50, 257982), 'exchange': 'kraken', 'symbol': 'XRP/USDT'}, 'coinbasepro:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 52, 350466), 'exchange': 'coinbasepro', 'symbol': 'BTC/USDT'}, 'binance:XRP/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 53, 829936), 'exchange': 'binance', 'symbol': 'XRP/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 9, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 4, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 1, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 4, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 55, 32767)}, returning basic analysis
2025-08-07 16:34:55,033 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:55,034 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:55,034 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:55,034 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:55,034 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:55,034 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:55,034 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:55,146 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:56,194 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:56,607 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:57,409 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:57,807 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:34:57,807 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:34:57,807 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:34:57,807 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:34:57,808 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573674426, 'datetime': '2025-08-07T13:34:34.426Z', 'high': 781.07, 'low': 760.07, 'bid': 775.5, 'bidVolume': None, 'ask': 778.1, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.10000', '1', '1.000'], 'b': ['775.50000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 41, 865077), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}, 'binance:BNB/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 44, 13037), 'exchange': 'binance', 'symbol': 'BNB/USDT'}, 'kraken:XRP/USDT': {'ticker': {'symbol': 'XRP/USDT', 'timestamp': 1754573682959, 'datetime': '2025-08-07T13:34:42.959Z', 'high': 3.10334, 'low': 2.93504, 'bid': 3.07459, 'bidVolume': None, 'ask': 3.0746, 'askVolume': None, 'vwap': 3.02490947, 'open': 2.98332, 'close': 3.07069, 'last': 3.07069, 'previousClose': None, 'change': 0.08737, 'percentage': 2.9286164407438693, 'average': 3.027005, 'baseVolume': 1012487.20808822, 'quoteVolume': 3062682.1439999174, 'info': {'a': ['3.07460000', '68', '68.000'], 'b': ['3.07459000', '127', '127.000'], 'c': ['3.07069000', '8.96631050'], 'v': ['640727.79090313', '1012487.20808822'], 'p': ['3.05635373', '3.02490947'], 't': ['1110', '1988'], 'l': ['2.96751000', '2.93504000'], 'h': ['3.10334000', '3.10334000'], 'o': '2.98332000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 50, 257982), 'exchange': 'kraken', 'symbol': 'XRP/USDT'}, 'coinbasepro:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 52, 350466), 'exchange': 'coinbasepro', 'symbol': 'BTC/USDT'}, 'binance:XRP/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 53, 829936), 'exchange': 'binance', 'symbol': 'XRP/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 9, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 4, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 1, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 4, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 57, 807117)}, returning basic analysis
2025-08-07 16:34:57,808 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:57,808 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:57,809 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:57,809 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:57,809 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:34:57,809 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:34:57,810 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:34:57,930 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:34:58,746 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:34:58,777 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:34:59,401 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:35:00,596 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:35:00,597 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:35:00,597 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:35:00,597 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:35:00,598 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573674426, 'datetime': '2025-08-07T13:34:34.426Z', 'high': 781.07, 'low': 760.07, 'bid': 775.5, 'bidVolume': None, 'ask': 778.1, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.10000', '1', '1.000'], 'b': ['775.50000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 41, 865077), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}, 'binance:BNB/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 44, 13037), 'exchange': 'binance', 'symbol': 'BNB/USDT'}, 'kraken:XRP/USDT': {'ticker': {'symbol': 'XRP/USDT', 'timestamp': 1754573682959, 'datetime': '2025-08-07T13:34:42.959Z', 'high': 3.10334, 'low': 2.93504, 'bid': 3.07459, 'bidVolume': None, 'ask': 3.0746, 'askVolume': None, 'vwap': 3.02490947, 'open': 2.98332, 'close': 3.07069, 'last': 3.07069, 'previousClose': None, 'change': 0.08737, 'percentage': 2.9286164407438693, 'average': 3.027005, 'baseVolume': 1012487.20808822, 'quoteVolume': 3062682.1439999174, 'info': {'a': ['3.07460000', '68', '68.000'], 'b': ['3.07459000', '127', '127.000'], 'c': ['3.07069000', '8.96631050'], 'v': ['640727.79090313', '1012487.20808822'], 'p': ['3.05635373', '3.02490947'], 't': ['1110', '1988'], 'l': ['2.96751000', '2.93504000'], 'h': ['3.10334000', '3.10334000'], 'o': '2.98332000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 50, 257982), 'exchange': 'kraken', 'symbol': 'XRP/USDT'}, 'coinbasepro:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 52, 350466), 'exchange': 'coinbasepro', 'symbol': 'BTC/USDT'}, 'binance:XRP/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 53, 829936), 'exchange': 'binance', 'symbol': 'XRP/USDT'}, 'kraken:ADA/USDT': {'ticker': {'symbol': 'ADA/USDT', 'timestamp': 1754573691339, 'datetime': '2025-08-07T13:34:51.339Z', 'high': 0.773544, 'low': 0.724329, 'bid': 0.768554, 'bidVolume': None, 'ask': 0.768921, 'askVolume': None, 'vwap': 0.74437229, 'open': 0.741417, 'close': 0.767547, 'last': 0.767547, 'previousClose': None, 'change': 0.02613, 'percentage': 3.524332460680022, 'average': 0.754482, 'baseVolume': 642195.98001289, 'quoteVolume': 478032.89227098913, 'info': {'a': ['0.76892100', '1040', '1040.000'], 'b': ['0.76855400', '1458', '1458.000'], 'c': ['0.76754700', '310.12119812'], 'v': ['249589.29456045', '642195.98001289'], 'p': ['0.75207218', '0.74437229'], 't': ['470', '865'], 'l': ['0.73447600', '0.72432900'], 'h': ['0.77354400', '0.77354400'], 'o': '0.74141700'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 58, 746800), 'exchange': 'kraken', 'symbol': 'ADA/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 10, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 4, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 1, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 5, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 35, 0, 597453)}, returning basic analysis
2025-08-07 16:35:00,598 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:00,599 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:00,599 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:00,599 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:00,599 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:00,599 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:35:00,600 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:35:00,733 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:35:01,089 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:35:02,192 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:35:02,293 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:35:03,385 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:35:03,385 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:35:03,386 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:35:03,386 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:35:03,386 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573674426, 'datetime': '2025-08-07T13:34:34.426Z', 'high': 781.07, 'low': 760.07, 'bid': 775.5, 'bidVolume': None, 'ask': 778.1, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.10000', '1', '1.000'], 'b': ['775.50000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 41, 865077), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}, 'binance:BNB/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 44, 13037), 'exchange': 'binance', 'symbol': 'BNB/USDT'}, 'kraken:XRP/USDT': {'ticker': {'symbol': 'XRP/USDT', 'timestamp': 1754573682959, 'datetime': '2025-08-07T13:34:42.959Z', 'high': 3.10334, 'low': 2.93504, 'bid': 3.07459, 'bidVolume': None, 'ask': 3.0746, 'askVolume': None, 'vwap': 3.02490947, 'open': 2.98332, 'close': 3.07069, 'last': 3.07069, 'previousClose': None, 'change': 0.08737, 'percentage': 2.9286164407438693, 'average': 3.027005, 'baseVolume': 1012487.20808822, 'quoteVolume': 3062682.1439999174, 'info': {'a': ['3.07460000', '68', '68.000'], 'b': ['3.07459000', '127', '127.000'], 'c': ['3.07069000', '8.96631050'], 'v': ['640727.79090313', '1012487.20808822'], 'p': ['3.05635373', '3.02490947'], 't': ['1110', '1988'], 'l': ['2.96751000', '2.93504000'], 'h': ['3.10334000', '3.10334000'], 'o': '2.98332000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 50, 257982), 'exchange': 'kraken', 'symbol': 'XRP/USDT'}, 'coinbasepro:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 52, 350466), 'exchange': 'coinbasepro', 'symbol': 'BTC/USDT'}, 'binance:XRP/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 53, 829936), 'exchange': 'binance', 'symbol': 'XRP/USDT'}, 'kraken:ADA/USDT': {'ticker': {'symbol': 'ADA/USDT', 'timestamp': 1754573691339, 'datetime': '2025-08-07T13:34:51.339Z', 'high': 0.773544, 'low': 0.724329, 'bid': 0.768554, 'bidVolume': None, 'ask': 0.768921, 'askVolume': None, 'vwap': 0.74437229, 'open': 0.741417, 'close': 0.767547, 'last': 0.767547, 'previousClose': None, 'change': 0.02613, 'percentage': 3.524332460680022, 'average': 0.754482, 'baseVolume': 642195.98001289, 'quoteVolume': 478032.89227098913, 'info': {'a': ['0.76892100', '1040', '1040.000'], 'b': ['0.76855400', '1458', '1458.000'], 'c': ['0.76754700', '310.12119812'], 'v': ['249589.29456045', '642195.98001289'], 'p': ['0.75207218', '0.74437229'], 't': ['470', '865'], 'l': ['0.73447600', '0.72432900'], 'h': ['0.77354400', '0.77354400'], 'o': '0.74141700'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 58, 746800), 'exchange': 'kraken', 'symbol': 'ADA/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 10, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 4, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 1, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 5, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 35, 3, 386366)}, returning basic analysis
2025-08-07 16:35:03,388 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:03,388 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:03,389 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:03,389 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:03,389 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:03,389 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:35:03,390 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:35:03,503 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:35:03,560 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:35:04,727 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:35:04,943 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:35:04,971 - src.exchanges.exchange_manager - ERROR - Failed to get ticker SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:35:05,906 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:35:06,170 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:35:06,170 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:35:06,170 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:35:06,170 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:35:06,171 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573674426, 'datetime': '2025-08-07T13:34:34.426Z', 'high': 781.07, 'low': 760.07, 'bid': 775.5, 'bidVolume': None, 'ask': 778.1, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.10000', '1', '1.000'], 'b': ['775.50000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 41, 865077), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}, 'binance:BNB/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 44, 13037), 'exchange': 'binance', 'symbol': 'BNB/USDT'}, 'kraken:XRP/USDT': {'ticker': {'symbol': 'XRP/USDT', 'timestamp': 1754573682959, 'datetime': '2025-08-07T13:34:42.959Z', 'high': 3.10334, 'low': 2.93504, 'bid': 3.07459, 'bidVolume': None, 'ask': 3.0746, 'askVolume': None, 'vwap': 3.02490947, 'open': 2.98332, 'close': 3.07069, 'last': 3.07069, 'previousClose': None, 'change': 0.08737, 'percentage': 2.9286164407438693, 'average': 3.027005, 'baseVolume': 1012487.20808822, 'quoteVolume': 3062682.1439999174, 'info': {'a': ['3.07460000', '68', '68.000'], 'b': ['3.07459000', '127', '127.000'], 'c': ['3.07069000', '8.96631050'], 'v': ['640727.79090313', '1012487.20808822'], 'p': ['3.05635373', '3.02490947'], 't': ['1110', '1988'], 'l': ['2.96751000', '2.93504000'], 'h': ['3.10334000', '3.10334000'], 'o': '2.98332000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 50, 257982), 'exchange': 'kraken', 'symbol': 'XRP/USDT'}, 'coinbasepro:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 52, 350466), 'exchange': 'coinbasepro', 'symbol': 'BTC/USDT'}, 'binance:XRP/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 53, 829936), 'exchange': 'binance', 'symbol': 'XRP/USDT'}, 'kraken:ADA/USDT': {'ticker': {'symbol': 'ADA/USDT', 'timestamp': 1754573691339, 'datetime': '2025-08-07T13:34:51.339Z', 'high': 0.773544, 'low': 0.724329, 'bid': 0.768554, 'bidVolume': None, 'ask': 0.768921, 'askVolume': None, 'vwap': 0.74437229, 'open': 0.741417, 'close': 0.767547, 'last': 0.767547, 'previousClose': None, 'change': 0.02613, 'percentage': 3.524332460680022, 'average': 0.754482, 'baseVolume': 642195.98001289, 'quoteVolume': 478032.89227098913, 'info': {'a': ['0.76892100', '1040', '1040.000'], 'b': ['0.76855400', '1458', '1458.000'], 'c': ['0.76754700', '310.12119812'], 'v': ['249589.29456045', '642195.98001289'], 'p': ['0.75207218', '0.74437229'], 't': ['470', '865'], 'l': ['0.73447600', '0.72432900'], 'h': ['0.77354400', '0.77354400'], 'o': '0.74141700'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 58, 746800), 'exchange': 'kraken', 'symbol': 'ADA/USDT'}, 'binance:ADA/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 35, 3, 503945), 'exchange': 'binance', 'symbol': 'ADA/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 11, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 5, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 1, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 5, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 35, 6, 171866)}, returning basic analysis
2025-08-07 16:35:06,171 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:06,172 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:06,172 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:06,172 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:06,172 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:06,172 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:35:06,174 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:35:06,286 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:35:07,139 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:35:07,753 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:35:08,946 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:35:08,946 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:35:08,946 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:35:08,946 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:35:08,946 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573674426, 'datetime': '2025-08-07T13:34:34.426Z', 'high': 781.07, 'low': 760.07, 'bid': 775.5, 'bidVolume': None, 'ask': 778.1, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.10000', '1', '1.000'], 'b': ['775.50000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 41, 865077), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}, 'binance:BNB/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 44, 13037), 'exchange': 'binance', 'symbol': 'BNB/USDT'}, 'kraken:XRP/USDT': {'ticker': {'symbol': 'XRP/USDT', 'timestamp': 1754573682959, 'datetime': '2025-08-07T13:34:42.959Z', 'high': 3.10334, 'low': 2.93504, 'bid': 3.07459, 'bidVolume': None, 'ask': 3.0746, 'askVolume': None, 'vwap': 3.02490947, 'open': 2.98332, 'close': 3.07069, 'last': 3.07069, 'previousClose': None, 'change': 0.08737, 'percentage': 2.9286164407438693, 'average': 3.027005, 'baseVolume': 1012487.20808822, 'quoteVolume': 3062682.1439999174, 'info': {'a': ['3.07460000', '68', '68.000'], 'b': ['3.07459000', '127', '127.000'], 'c': ['3.07069000', '8.96631050'], 'v': ['640727.79090313', '1012487.20808822'], 'p': ['3.05635373', '3.02490947'], 't': ['1110', '1988'], 'l': ['2.96751000', '2.93504000'], 'h': ['3.10334000', '3.10334000'], 'o': '2.98332000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 50, 257982), 'exchange': 'kraken', 'symbol': 'XRP/USDT'}, 'coinbasepro:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 52, 350466), 'exchange': 'coinbasepro', 'symbol': 'BTC/USDT'}, 'binance:XRP/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 53, 829936), 'exchange': 'binance', 'symbol': 'XRP/USDT'}, 'kraken:ADA/USDT': {'ticker': {'symbol': 'ADA/USDT', 'timestamp': 1754573691339, 'datetime': '2025-08-07T13:34:51.339Z', 'high': 0.773544, 'low': 0.724329, 'bid': 0.768554, 'bidVolume': None, 'ask': 0.768921, 'askVolume': None, 'vwap': 0.74437229, 'open': 0.741417, 'close': 0.767547, 'last': 0.767547, 'previousClose': None, 'change': 0.02613, 'percentage': 3.524332460680022, 'average': 0.754482, 'baseVolume': 642195.98001289, 'quoteVolume': 478032.89227098913, 'info': {'a': ['0.76892100', '1040', '1040.000'], 'b': ['0.76855400', '1458', '1458.000'], 'c': ['0.76754700', '310.12119812'], 'v': ['249589.29456045', '642195.98001289'], 'p': ['0.75207218', '0.74437229'], 't': ['470', '865'], 'l': ['0.73447600', '0.72432900'], 'h': ['0.77354400', '0.77354400'], 'o': '0.74141700'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 58, 746800), 'exchange': 'kraken', 'symbol': 'ADA/USDT'}, 'binance:ADA/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 35, 3, 503945), 'exchange': 'binance', 'symbol': 'ADA/USDT'}, 'kraken:SOL/USDT': {'ticker': {'symbol': 'SOL/USDT', 'timestamp': 1754573699941, 'datetime': '2025-08-07T13:34:59.941Z', 'high': 173.38, 'low': 163.21, 'bid': 172.28, 'bidVolume': None, 'ask': 172.31, 'askVolume': None, 'vwap': 168.721531, 'open': 168.1, 'close': 172.05, 'last': 172.05, 'previousClose': None, 'change': 3.95, 'percentage': 2.349791790600833, 'average': 170.075, 'baseVolume': 9972.41489594, 'quoteVolume': 1682561.1090102026, 'info': {'a': ['172.310000', '53', '53.000'], 'b': ['172.280000', '8', '8.000'], 'c': ['172.050000', '8.71604087'], 'v': ['4810.71655328', '9972.41489594'], 'p': ['170.447042', '168.721531'], 't': ['991', '2262'], 'l': ['166.800000', '163.210000'], 'h': ['173.380000', '173.380000'], 'o': '168.100000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 35, 7, 139017), 'exchange': 'kraken', 'symbol': 'SOL/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 12, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 5, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 1, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 6, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 35, 8, 946935)}, returning basic analysis
2025-08-07 16:35:08,955 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:08,955 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:08,955 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:08,955 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:08,955 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:08,955 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:35:08,955 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:35:09,071 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:35:09,507 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:35:10,539 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:35:10,593 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:35:11,102 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from coinbasepro: coinbasepro GET https://api.pro.coinbase.com/currencies 503 Service Unavailable {"message":"Coinbase Pro API is deprecated."}
2025-08-07 16:35:11,736 - src.exchanges.exchange_manager - WARNING - Health check failed for coinbasepro: coinbasepro fetchStatus() is not supported yet
2025-08-07 16:35:11,737 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:35:11,737 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:35:11,737 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:35:11,738 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for {'data': {'binance:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 23, 185759), 'exchange': 'binance', 'symbol': 'BTC/USDT'}, 'kraken:BTC/USDT': {'ticker': {'symbol': 'BTC/USDT', 'timestamp': 1754573657402, 'datetime': '2025-08-07T13:34:17.402Z', 'high': 116825.7, 'low': 113652.2, 'bid': 116408.3, 'bidVolume': None, 'ask': 116419.7, 'askVolume': None, 'vwap': 115449.57684, 'open': 115003.4, 'close': 116414.2, 'last': 116414.2, 'previousClose': None, 'change': 1410.8, 'percentage': 1.2267463396734357, 'average': 115708.8, 'baseVolume': 48.8236722, 'quoteVolume': 5636672.295264872, 'info': {'a': ['116419.70000', '1', '1.000'], 'b': ['116408.30000', '1', '1.000'], 'c': ['116414.20000', '0.00953472'], 'v': ['26.28234107', '48.82367220'], 'p': ['115687.83999', '115449.57684'], 't': ['1247', '2395'], 'l': ['114266.10000', '113652.20000'], 'h': ['116825.70000', '116825.70000'], 'o': '115003.40000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 24, 770311), 'exchange': 'kraken', 'symbol': 'BTC/USDT'}, 'kraken:ETH/USDT': {'ticker': {'symbol': 'ETH/USDT', 'timestamp': 1754573665906, 'datetime': '2025-08-07T13:34:25.906Z', 'high': 3850.0, 'low': 3577.39, 'bid': 3837.69, 'bidVolume': None, 'ask': 3838.16, 'askVolume': None, 'vwap': 3711.29396, 'open': 3682.64, 'close': 3833.52, 'last': 3833.52, 'previousClose': None, 'change': 150.88, 'percentage': 4.097060804205679, 'average': 3758.08, 'baseVolume': 1992.08599984, 'quoteVolume': 7393216.739006753, 'info': {'a': ['3838.16000', '9', '9.000'], 'b': ['3837.69000', '2', '2.000'], 'c': ['3833.52000', '0.00000050'], 'v': ['988.20738154', '1992.08599984'], 'p': ['3773.42303', '3711.29396'], 't': ['1294', '2246'], 'l': ['3649.46000', '3577.39000'], 'h': ['3850.00000', '3850.00000'], 'o': '3682.64000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 251456), 'exchange': 'kraken', 'symbol': 'ETH/USDT'}, 'binance:ETH/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 33, 503326), 'exchange': 'binance', 'symbol': 'ETH/USDT'}, 'kraken:BNB/USDT': {'ticker': {'symbol': 'BNB/USDT', 'timestamp': 1754573674426, 'datetime': '2025-08-07T13:34:34.426Z', 'high': 781.07, 'low': 760.07, 'bid': 775.5, 'bidVolume': None, 'ask': 778.1, 'askVolume': None, 'vwap': 773.33836, 'open': 773.9, 'close': 773.97, 'last': 773.97, 'previousClose': None, 'change': 0.07, 'percentage': 0.0090450962656673, 'average': 773.935, 'baseVolume': 56.85216, 'quoteVolume': 43965.9561768576, 'info': {'a': ['778.10000', '1', '1.000'], 'b': ['775.50000', '1', '1.000'], 'c': ['773.97000', '0.01314'], 'v': ['29.72043', '56.85216'], 'p': ['776.33946', '773.33836'], 't': ['79', '143'], 'l': ['764.48000', '760.07000'], 'h': ['781.07000', '781.07000'], 'o': '773.90000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 41, 865077), 'exchange': 'kraken', 'symbol': 'BNB/USDT'}, 'binance:BNB/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 44, 13037), 'exchange': 'binance', 'symbol': 'BNB/USDT'}, 'kraken:XRP/USDT': {'ticker': {'symbol': 'XRP/USDT', 'timestamp': 1754573682959, 'datetime': '2025-08-07T13:34:42.959Z', 'high': 3.10334, 'low': 2.93504, 'bid': 3.07459, 'bidVolume': None, 'ask': 3.0746, 'askVolume': None, 'vwap': 3.02490947, 'open': 2.98332, 'close': 3.07069, 'last': 3.07069, 'previousClose': None, 'change': 0.08737, 'percentage': 2.9286164407438693, 'average': 3.027005, 'baseVolume': 1012487.20808822, 'quoteVolume': 3062682.1439999174, 'info': {'a': ['3.07460000', '68', '68.000'], 'b': ['3.07459000', '127', '127.000'], 'c': ['3.07069000', '8.96631050'], 'v': ['640727.79090313', '1012487.20808822'], 'p': ['3.05635373', '3.02490947'], 't': ['1110', '1988'], 'l': ['2.96751000', '2.93504000'], 'h': ['3.10334000', '3.10334000'], 'o': '2.98332000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 50, 257982), 'exchange': 'kraken', 'symbol': 'XRP/USDT'}, 'coinbasepro:BTC/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 52, 350466), 'exchange': 'coinbasepro', 'symbol': 'BTC/USDT'}, 'binance:XRP/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 53, 829936), 'exchange': 'binance', 'symbol': 'XRP/USDT'}, 'kraken:ADA/USDT': {'ticker': {'symbol': 'ADA/USDT', 'timestamp': 1754573691339, 'datetime': '2025-08-07T13:34:51.339Z', 'high': 0.773544, 'low': 0.724329, 'bid': 0.768554, 'bidVolume': None, 'ask': 0.768921, 'askVolume': None, 'vwap': 0.74437229, 'open': 0.741417, 'close': 0.767547, 'last': 0.767547, 'previousClose': None, 'change': 0.02613, 'percentage': 3.524332460680022, 'average': 0.754482, 'baseVolume': 642195.98001289, 'quoteVolume': 478032.89227098913, 'info': {'a': ['0.76892100', '1040', '1040.000'], 'b': ['0.76855400', '1458', '1458.000'], 'c': ['0.76754700', '310.12119812'], 'v': ['249589.29456045', '642195.98001289'], 'p': ['0.75207218', '0.74437229'], 't': ['470', '865'], 'l': ['0.73447600', '0.72432900'], 'h': ['0.77354400', '0.77354400'], 'o': '0.74141700'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 34, 58, 746800), 'exchange': 'kraken', 'symbol': 'ADA/USDT'}, 'binance:ADA/USDT': {'ticker': None, 'timestamp': datetime.datetime(2025, 8, 7, 13, 35, 3, 503945), 'exchange': 'binance', 'symbol': 'ADA/USDT'}, 'kraken:SOL/USDT': {'ticker': {'symbol': 'SOL/USDT', 'timestamp': 1754573699941, 'datetime': '2025-08-07T13:34:59.941Z', 'high': 173.38, 'low': 163.21, 'bid': 172.28, 'bidVolume': None, 'ask': 172.31, 'askVolume': None, 'vwap': 168.721531, 'open': 168.1, 'close': 172.05, 'last': 172.05, 'previousClose': None, 'change': 3.95, 'percentage': 2.349791790600833, 'average': 170.075, 'baseVolume': 9972.41489594, 'quoteVolume': 1682561.1090102026, 'info': {'a': ['172.310000', '53', '53.000'], 'b': ['172.280000', '8', '8.000'], 'c': ['172.050000', '8.71604087'], 'v': ['4810.71655328', '9972.41489594'], 'p': ['170.447042', '168.721531'], 't': ['991', '2262'], 'l': ['166.800000', '163.210000'], 'h': ['173.380000', '173.380000'], 'o': '168.100000'}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 35, 7, 139017), 'exchange': 'kraken', 'symbol': 'SOL/USDT'}}, 'stats': {'total_updates': 0, 'successful_updates': 12, 'failed_updates': 0, 'last_update': None, 'exchanges': {'binance': {'updates': 5, 'errors': 0, 'last_update': None}, 'coinbasepro': {'updates': 1, 'errors': 0, 'last_update': None}, 'kraken': {'updates': 6, 'errors': 0, 'last_update': None}}}, 'timestamp': datetime.datetime(2025, 8, 7, 13, 35, 11, 738755)}, returning basic analysis
2025-08-07 16:35:11,738 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:11,739 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:11,739 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:11,739 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:11,739 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:35:11,740 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:35:11,740 - src.core.trading_engine - ERROR - Metrics update error: object float can't be used in 'await' expression
2025-08-07 16:35:11,857 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:35:11,940 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:35:13,174 - src.data.market_data_collector - ERROR - Failed to store OHLCV data: Database not initialized
2025-08-07 16:35:13,323 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:42:09,373 - __main__ - INFO - Version: 1.0.0
2025-08-07 16:42:09,373 - __main__ - INFO - Environment: development
2025-08-07 16:42:09,373 - __main__ - INFO - Paper Trading: True
2025-08-07 16:42:09,374 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 16:42:09,377 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 16:42:09,465 - src.config.security - INFO - Encryption system initialized
2025-08-07 16:42:09,550 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 16:42:09,568 - src.exchanges.exchange_manager - INFO - Configured exchanges: ['binance', 'kraken']
2025-08-07 16:42:10,977 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 16:42:10,978 - src.config.database - INFO - Initializing database connection...
2025-08-07 16:42:11,046 - src.config.database - INFO - Creating database tables...
2025-08-07 16:42:11,240 - src.data.market_data_collector - INFO - Added trading pair: BTC/USDT on kraken
2025-08-07 16:42:11,243 - src.data.market_data_collector - INFO - Added trading pair: ETH/USDT on kraken
2025-08-07 16:42:11,246 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on binance
2025-08-07 16:42:11,248 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on kraken
2025-08-07 16:42:11,250 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on binance
2025-08-07 16:42:11,253 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on kraken
2025-08-07 16:42:11,257 - src.data.market_data_collector - INFO - Added trading pair: ADA/USDT on kraken
2025-08-07 16:42:11,259 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on binance
2025-08-07 16:42:11,261 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on kraken
2025-08-07 16:42:11,264 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on binance
2025-08-07 16:42:11,266 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on kraken
2025-08-07 16:42:11,270 - src.data.market_data_collector - INFO - Added trading pair: DOT/USDT on kraken
2025-08-07 16:42:11,272 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on binance
2025-08-07 16:42:11,274 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on kraken
2025-08-07 16:42:11,276 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on binance
2025-08-07 16:42:11,278 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on kraken
2025-08-07 16:42:11,282 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on binance
2025-08-07 16:42:11,284 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on kraken
2025-08-07 16:42:11,288 - src.data.market_data_collector - INFO - Added trading pair: LINK/USDT on kraken
2025-08-07 16:42:11,290 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on binance
2025-08-07 16:42:11,292 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on kraken
2025-08-07 16:42:11,294 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on binance
2025-08-07 16:42:11,296 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on kraken
2025-08-07 16:42:11,299 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on binance
2025-08-07 16:42:11,301 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on kraken
2025-08-07 16:42:11,302 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on binance
2025-08-07 16:42:11,305 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on kraken
2025-08-07 16:42:11,307 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on binance
2025-08-07 16:42:11,309 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on kraken
2025-08-07 16:42:11,312 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on binance
2025-08-07 16:42:11,315 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on kraken
2025-08-07 16:42:11,317 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on binance
2025-08-07 16:42:11,318 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on kraken
2025-08-07 16:42:11,321 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on binance
2025-08-07 16:42:11,323 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on kraken
2025-08-07 16:42:11,325 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on binance
2025-08-07 16:42:11,327 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on kraken
2025-08-07 16:42:11,329 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on binance
2025-08-07 16:42:11,332 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on kraken
2025-08-07 16:42:11,334 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on binance
2025-08-07 16:42:11,337 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on kraken
2025-08-07 16:42:11,339 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on binance
2025-08-07 16:42:11,342 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on kraken
2025-08-07 16:42:11,349 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on binance
2025-08-07 16:42:11,352 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on kraken
2025-08-07 16:42:11,355 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on binance
2025-08-07 16:42:11,359 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on kraken
2025-08-07 16:42:11,362 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on binance
2025-08-07 16:42:11,366 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on kraken
2025-08-07 16:42:11,377 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on binance
2025-08-07 16:42:11,380 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on kraken
2025-08-07 16:42:11,383 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on binance
2025-08-07 16:42:11,386 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on kraken
2025-08-07 16:42:11,389 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on binance
2025-08-07 16:42:11,394 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on kraken
2025-08-07 16:42:11,422 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "ix_trading_pairs_symbol"
DETAIL:  Key (symbol)=(BTC/USDT) already exists.

[SQL: INSERT INTO trading_pairs (id, symbol, base_asset, quote_asset, exchange, is_active, min_order_size, max_order_size, price_precision, quantity_precision) VALUES (%(id__0)s::UUID, %(symbol__0)s, %(base_asset__0)s, %(quote_asset__0)s, %(exchange__0)s,  ... 11613 characters truncated ... tity_precision__54)s) RETURNING trading_pairs.created_at, trading_pairs.updated_at, trading_pairs.id]
[parameters: {'quantity_precision__0': 8, 'id__0': UUID('a23ddaa0-cafd-4f3b-8e58-381e86aaa43f'), 'max_order_size__0': None, 'quote_asset__0': 'USDT', 'symbol__0': 'BTC/USDT', 'price_precision__0': 8, 'base_asset__0': 'BTC', 'exchange__0': 'kraken', 'is_active__0': True, 'min_order_size__0': None, 'quantity_precision__1': 8, 'id__1': UUID('61696ee5-30bc-4ccc-8133-21e78e6b976e'), 'max_order_size__1': None, 'quote_asset__1': 'USDT', 'symbol__1': 'ETH/USDT', 'price_precision__1': 8, 'base_asset__1': 'ETH', 'exchange__1': 'kraken', 'is_active__1': True, 'min_order_size__1': None, 'quantity_precision__2': 8, 'id__2': UUID('9528b177-3046-481d-9608-e4b9ff3452d2'), 'max_order_size__2': None, 'quote_asset__2': 'USDT', 'symbol__2': 'BNB/USDT', 'price_precision__2': 8, 'base_asset__2': 'BNB', 'exchange__2': 'binance', 'is_active__2': True, 'min_order_size__2': None, 'quantity_precision__3': 8, 'id__3': UUID('9e7317e7-85ee-48db-ba96-de634ff9b4d5'), 'max_order_size__3': None, 'quote_asset__3': 'USDT', 'symbol__3': 'BNB/USDT', 'price_precision__3': 8, 'base_asset__3': 'BNB', 'exchange__3': 'kraken', 'is_active__3': True, 'min_order_size__3': None, 'quantity_precision__4': 8, 'id__4': UUID('3713d958-31db-4d65-989d-4c0b3895d469'), 'max_order_size__4': None, 'quote_asset__4': 'USDT', 'symbol__4': 'XRP/USDT', 'price_precision__4': 8, 'base_asset__4': 'XRP', 'exchange__4': 'binance', 'is_active__4': True, 'min_order_size__4': None ... 450 parameters truncated ... 'quantity_precision__50': 8, 'id__50': UUID('cc174fcc-4774-45c4-b16b-af5ac6b3dc32'), 'max_order_size__50': None, 'quote_asset__50': 'USDT', 'symbol__50': 'TRX/USDT', 'price_precision__50': 8, 'base_asset__50': 'TRX', 'exchange__50': 'kraken', 'is_active__50': True, 'min_order_size__50': None, 'quantity_precision__51': 8, 'id__51': UUID('87976ffb-8639-41a4-a8c2-cdbc68bb8a1a'), 'max_order_size__51': None, 'quote_asset__51': 'USDT', 'symbol__51': 'EOS/USDT', 'price_precision__51': 8, 'base_asset__51': 'EOS', 'exchange__51': 'binance', 'is_active__51': True, 'min_order_size__51': None, 'quantity_precision__52': 8, 'id__52': UUID('ee026aeb-accf-4630-9885-29dcbfd28a5b'), 'max_order_size__52': None, 'quote_asset__52': 'USDT', 'symbol__52': 'EOS/USDT', 'price_precision__52': 8, 'base_asset__52': 'EOS', 'exchange__52': 'kraken', 'is_active__52': True, 'min_order_size__52': None, 'quantity_precision__53': 8, 'id__53': UUID('48d77929-0847-43eb-ac90-74105f2d19a5'), 'max_order_size__53': None, 'quote_asset__53': 'USDT', 'symbol__53': 'IOTA/USDT', 'price_precision__53': 8, 'base_asset__53': 'IOTA', 'exchange__53': 'binance', 'is_active__53': True, 'min_order_size__53': None, 'quantity_precision__54': 8, 'id__54': UUID('a1353cf4-d6ac-45a6-ab9a-ec69d6a37e8e'), 'max_order_size__54': None, 'quote_asset__54': 'USDT', 'symbol__54': 'IOTA/USDT', 'price_precision__54': 8, 'base_asset__54': 'IOTA', 'exchange__54': 'kraken', 'is_active__54': True, 'min_order_size__54': None}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-07 16:42:11,423 - src.data.market_data_collector - INFO - Market Data Collector initialized
2025-08-07 16:42:11,423 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 16:42:11,423 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 16:42:11,919 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:42:11,955 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 16:42:11,956 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 16:42:12,067 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:42:12,137 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 16:42:12,137 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 16:42:12,143 - src.core.ai_agent - INFO - AI Trading Agent initialized successfully
2025-08-07 16:42:12,143 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 16:42:12,146 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 16:42:12,151 - src.core.trading_engine - INFO - Trading Engine initialized successfully
2025-08-07 16:42:12,151 - src.core.trading_engine - INFO - Starting Trading Engine...
2025-08-07 16:42:12,156 - src.core.trading_engine - INFO - Trading Engine started successfully
2025-08-07 16:42:12,159 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 16:42:12,160 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 16:42:12,160 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 16:42:12,555 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:42:12,555 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:42:12,556 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:42:12,556 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:12,556 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:12,556 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:12,556 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:12,556 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:12,557 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:42:12,677 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:42:14,283 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:42:15,346 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:42:15,346 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:42:15,347 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:42:15,347 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:15,347 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:15,347 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:15,347 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:15,348 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:15,348 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:42:15,474 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:42:16,532 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:42:16,926 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:42:17,639 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:42:18,119 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:42:18,119 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:42:18,120 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:42:18,120 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:18,121 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:18,121 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:18,121 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:18,121 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:18,121 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:42:18,268 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:42:18,782 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:42:19,720 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:42:19,959 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:42:20,919 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:42:20,919 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:42:20,919 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:42:20,920 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:20,920 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:20,920 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:20,920 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:20,920 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:20,920 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:42:21,051 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:42:21,148 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:42:22,308 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:42:22,514 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:42:23,708 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:42:23,709 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:42:23,709 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:42:23,709 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:42:23,709 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:23,710 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:23,710 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:23,710 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:23,710 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:23,710 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:42:23,835 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:42:24,762 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 16:42:25,293 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:42:26,035 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 16:42:26,499 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:42:26,501 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:42:26,501 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:42:26,501 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:42:26,501 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:26,501 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:26,502 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:26,502 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:26,502 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:42:26,502 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:42:26,616 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:46:50,885 - __main__ - INFO - Version: 1.0.0
2025-08-07 16:46:50,885 - __main__ - INFO - Environment: development
2025-08-07 16:46:50,885 - __main__ - INFO - Paper Trading: True
2025-08-07 16:46:50,886 - src.monitoring.health_checker - INFO - Starting Health Checker...
2025-08-07 16:46:50,887 - src.core.trading_engine - INFO - Initializing Trading Engine components...
2025-08-07 16:46:50,971 - src.config.security - INFO - Encryption system initialized
2025-08-07 16:46:51,057 - src.exchanges.exchange_manager - INFO - Initializing Exchange Manager...
2025-08-07 16:46:51,073 - src.exchanges.exchange_manager - INFO - Configured exchanges: ['binance', 'kraken']
2025-08-07 16:46:52,510 - src.data.market_data_collector - INFO - Initializing Market Data Collector...
2025-08-07 16:46:52,511 - src.config.database - INFO - Initializing database connection...
2025-08-07 16:46:52,568 - src.config.database - INFO - Creating database tables...
2025-08-07 16:46:52,636 - src.data.market_data_collector - INFO - Added trading pair: BTC/USDT on kraken
2025-08-07 16:46:52,640 - src.data.market_data_collector - INFO - Added trading pair: ETH/USDT on kraken
2025-08-07 16:46:52,641 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on binance
2025-08-07 16:46:52,644 - src.data.market_data_collector - INFO - Added trading pair: BNB/USDT on kraken
2025-08-07 16:46:52,646 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on binance
2025-08-07 16:46:52,648 - src.data.market_data_collector - INFO - Added trading pair: XRP/USDT on kraken
2025-08-07 16:46:52,651 - src.data.market_data_collector - INFO - Added trading pair: ADA/USDT on kraken
2025-08-07 16:46:52,654 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on binance
2025-08-07 16:46:52,657 - src.data.market_data_collector - INFO - Added trading pair: SOL/USDT on kraken
2025-08-07 16:46:52,659 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on binance
2025-08-07 16:46:52,661 - src.data.market_data_collector - INFO - Added trading pair: DOGE/USDT on kraken
2025-08-07 16:46:52,665 - src.data.market_data_collector - INFO - Added trading pair: DOT/USDT on kraken
2025-08-07 16:46:52,666 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on binance
2025-08-07 16:46:52,668 - src.data.market_data_collector - INFO - Added trading pair: MATIC/USDT on kraken
2025-08-07 16:46:52,670 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on binance
2025-08-07 16:46:52,674 - src.data.market_data_collector - INFO - Added trading pair: AVAX/USDT on kraken
2025-08-07 16:46:52,676 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on binance
2025-08-07 16:46:52,679 - src.data.market_data_collector - INFO - Added trading pair: UNI/USDT on kraken
2025-08-07 16:46:52,683 - src.data.market_data_collector - INFO - Added trading pair: LINK/USDT on kraken
2025-08-07 16:46:52,686 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on binance
2025-08-07 16:46:52,689 - src.data.market_data_collector - INFO - Added trading pair: AAVE/USDT on kraken
2025-08-07 16:46:52,691 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on binance
2025-08-07 16:46:52,694 - src.data.market_data_collector - INFO - Added trading pair: SUSHI/USDT on kraken
2025-08-07 16:46:52,696 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on binance
2025-08-07 16:46:52,699 - src.data.market_data_collector - INFO - Added trading pair: COMP/USDT on kraken
2025-08-07 16:46:52,701 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on binance
2025-08-07 16:46:52,704 - src.data.market_data_collector - INFO - Added trading pair: ATOM/USDT on kraken
2025-08-07 16:46:52,707 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on binance
2025-08-07 16:46:52,709 - src.data.market_data_collector - INFO - Added trading pair: ALGO/USDT on kraken
2025-08-07 16:46:52,711 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on binance
2025-08-07 16:46:52,715 - src.data.market_data_collector - INFO - Added trading pair: NEAR/USDT on kraken
2025-08-07 16:46:52,717 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on binance
2025-08-07 16:46:52,719 - src.data.market_data_collector - INFO - Added trading pair: FTM/USDT on kraken
2025-08-07 16:46:52,722 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on binance
2025-08-07 16:46:52,724 - src.data.market_data_collector - INFO - Added trading pair: ONE/USDT on kraken
2025-08-07 16:46:52,727 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on binance
2025-08-07 16:46:52,729 - src.data.market_data_collector - INFO - Added trading pair: LTC/USDT on kraken
2025-08-07 16:46:52,732 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on binance
2025-08-07 16:46:52,734 - src.data.market_data_collector - INFO - Added trading pair: BCH/USDT on kraken
2025-08-07 16:46:52,736 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on binance
2025-08-07 16:46:52,739 - src.data.market_data_collector - INFO - Added trading pair: ETC/USDT on kraken
2025-08-07 16:46:52,742 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on binance
2025-08-07 16:46:52,744 - src.data.market_data_collector - INFO - Added trading pair: XLM/USDT on kraken
2025-08-07 16:46:52,746 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on binance
2025-08-07 16:46:52,749 - src.data.market_data_collector - INFO - Added trading pair: VET/USDT on kraken
2025-08-07 16:46:52,751 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on binance
2025-08-07 16:46:52,753 - src.data.market_data_collector - INFO - Added trading pair: THETA/USDT on kraken
2025-08-07 16:46:52,755 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on binance
2025-08-07 16:46:52,758 - src.data.market_data_collector - INFO - Added trading pair: FIL/USDT on kraken
2025-08-07 16:46:52,761 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on binance
2025-08-07 16:46:52,763 - src.data.market_data_collector - INFO - Added trading pair: TRX/USDT on kraken
2025-08-07 16:46:52,765 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on binance
2025-08-07 16:46:52,767 - src.data.market_data_collector - INFO - Added trading pair: EOS/USDT on kraken
2025-08-07 16:46:52,769 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on binance
2025-08-07 16:46:52,771 - src.data.market_data_collector - INFO - Added trading pair: IOTA/USDT on kraken
2025-08-07 16:46:52,782 - src.data.market_data_collector - ERROR - Failed to ensure trading pairs: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "ix_trading_pairs_symbol"
DETAIL:  Key (symbol)=(BTC/USDT) already exists.

[SQL: INSERT INTO trading_pairs (id, symbol, base_asset, quote_asset, exchange, is_active, min_order_size, max_order_size, price_precision, quantity_precision) VALUES (%(id__0)s::UUID, %(symbol__0)s, %(base_asset__0)s, %(quote_asset__0)s, %(exchange__0)s,  ... 11613 characters truncated ... tity_precision__54)s) RETURNING trading_pairs.created_at, trading_pairs.updated_at, trading_pairs.id]
[parameters: {'id__0': UUID('0b6ebfda-15e0-41ff-868b-e8c00429a990'), 'exchange__0': 'kraken', 'base_asset__0': 'BTC', 'is_active__0': True, 'max_order_size__0': None, 'quantity_precision__0': 8, 'quote_asset__0': 'USDT', 'symbol__0': 'BTC/USDT', 'price_precision__0': 8, 'min_order_size__0': None, 'id__1': UUID('f840b1dc-55ac-4200-8bd7-7728a2bd52b7'), 'exchange__1': 'kraken', 'base_asset__1': 'ETH', 'is_active__1': True, 'max_order_size__1': None, 'quantity_precision__1': 8, 'quote_asset__1': 'USDT', 'symbol__1': 'ETH/USDT', 'price_precision__1': 8, 'min_order_size__1': None, 'id__2': UUID('d4285f26-0d38-4902-a77d-59dd000dbee1'), 'exchange__2': 'binance', 'base_asset__2': 'BNB', 'is_active__2': True, 'max_order_size__2': None, 'quantity_precision__2': 8, 'quote_asset__2': 'USDT', 'symbol__2': 'BNB/USDT', 'price_precision__2': 8, 'min_order_size__2': None, 'id__3': UUID('571976d7-d63d-4dde-8c12-ffaa29ae1d0b'), 'exchange__3': 'kraken', 'base_asset__3': 'BNB', 'is_active__3': True, 'max_order_size__3': None, 'quantity_precision__3': 8, 'quote_asset__3': 'USDT', 'symbol__3': 'BNB/USDT', 'price_precision__3': 8, 'min_order_size__3': None, 'id__4': UUID('877c626b-4e81-48ae-af84-04fa4dbe3dc0'), 'exchange__4': 'binance', 'base_asset__4': 'XRP', 'is_active__4': True, 'max_order_size__4': None, 'quantity_precision__4': 8, 'quote_asset__4': 'USDT', 'symbol__4': 'XRP/USDT', 'price_precision__4': 8, 'min_order_size__4': None ... 450 parameters truncated ... 'id__50': UUID('4485189c-12db-4ce9-a07d-5dd379bedb38'), 'exchange__50': 'kraken', 'base_asset__50': 'TRX', 'is_active__50': True, 'max_order_size__50': None, 'quantity_precision__50': 8, 'quote_asset__50': 'USDT', 'symbol__50': 'TRX/USDT', 'price_precision__50': 8, 'min_order_size__50': None, 'id__51': UUID('edeecf05-**************-0ff281ed6d28'), 'exchange__51': 'binance', 'base_asset__51': 'EOS', 'is_active__51': True, 'max_order_size__51': None, 'quantity_precision__51': 8, 'quote_asset__51': 'USDT', 'symbol__51': 'EOS/USDT', 'price_precision__51': 8, 'min_order_size__51': None, 'id__52': UUID('bb131dd9-3dfc-46dd-992f-3f751f8b6089'), 'exchange__52': 'kraken', 'base_asset__52': 'EOS', 'is_active__52': True, 'max_order_size__52': None, 'quantity_precision__52': 8, 'quote_asset__52': 'USDT', 'symbol__52': 'EOS/USDT', 'price_precision__52': 8, 'min_order_size__52': None, 'id__53': UUID('6af4a4a5-73e4-4838-ae78-4e098138fc45'), 'exchange__53': 'binance', 'base_asset__53': 'IOTA', 'is_active__53': True, 'max_order_size__53': None, 'quantity_precision__53': 8, 'quote_asset__53': 'USDT', 'symbol__53': 'IOTA/USDT', 'price_precision__53': 8, 'min_order_size__53': None, 'id__54': UUID('5b165c91-ba7e-45b0-ab59-07adb35b8c58'), 'exchange__54': 'kraken', 'base_asset__54': 'IOTA', 'is_active__54': True, 'max_order_size__54': None, 'quantity_precision__54': 8, 'quote_asset__54': 'USDT', 'symbol__54': 'IOTA/USDT', 'price_precision__54': 8, 'min_order_size__54': None}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-08-07 16:46:52,782 - src.data.market_data_collector - INFO - Market Data Collector initialized
2025-08-07 16:46:52,782 - src.core.ai_agent - INFO - Initializing AI Trading Agent...
2025-08-07 16:46:52,783 - src.ai.ai_manager - INFO - Initializing AI Manager...
2025-08-07 16:46:53,338 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:46:53,443 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openrouter
2025-08-07 16:46:53,444 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openrouter provider
2025-08-07 16:46:53,588 - httpx - INFO - HTTP Request: GET https://openrouter.ai/api/v1/models "HTTP/1.1 200 OK"
2025-08-07 16:46:53,669 - src.ai.providers.base_provider.openai - INFO - Connection test successful for openai
2025-08-07 16:46:53,670 - src.ai.providers.base_provider.openai - INFO - Successfully initialized openai provider
2025-08-07 16:46:53,676 - src.core.ai_agent - INFO - AI Trading Agent initialized successfully
2025-08-07 16:46:53,676 - src.core.strategy_manager - INFO - Initializing Strategy Manager...
2025-08-07 16:46:53,679 - src.core.order_manager - INFO - Initializing Order Manager...
2025-08-07 16:46:53,683 - src.core.trading_engine - INFO - Trading Engine initialized successfully
2025-08-07 16:46:53,683 - src.core.trading_engine - INFO - Starting Trading Engine...
2025-08-07 16:46:53,688 - src.core.trading_engine - INFO - Trading Engine started successfully
2025-08-07 16:46:53,692 - src.data.market_data_collector - INFO - Starting collection loop for binance
2025-08-07 16:46:53,692 - src.data.market_data_collector - INFO - Starting collection loop for kraken
2025-08-07 16:46:53,692 - src.core.trading_engine - INFO - Starting main trading loop...
2025-08-07 16:46:54,086 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:46:54,087 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:46:54,087 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:46:54,087 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:54,087 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:54,087 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:54,087 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:54,088 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:54,088 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:46:54,230 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:46:55,694 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:46:56,894 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:46:56,894 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:46:56,895 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:46:56,895 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:56,895 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:56,895 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:56,896 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:56,896 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:56,896 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:46:57,028 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:46:57,606 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:46:58,486 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:46:58,763 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:46:59,683 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:46:59,720 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:46:59,838 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:46:59,888 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:59,890 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:59,890 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:59,891 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:59,892 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:46:59,892 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:46:59,893 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:00,007 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:47:00,989 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:47:01,362 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:02,327 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:47:02,565 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:02,565 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:02,566 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:02,566 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:02,566 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:02,566 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:02,566 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:02,566 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:02,567 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:02,680 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BTC/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:03,550 - src.data.market_data_collector - WARNING - Trading pair not found: BTC/USDT on kraken
2025-08-07 16:47:04,144 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:05,336 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:05,338 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:05,338 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:05,338 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:05,339 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:05,339 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:05,339 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:05,339 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:05,339 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:05,339 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:05,466 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:05,841 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 16:47:06,927 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:07,214 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 16:47:08,124 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:08,124 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:08,124 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:08,124 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:08,124 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:08,124 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:08,125 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:08,125 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:08,125 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:08,125 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:08,258 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:08,428 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 16:47:09,705 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 16:47:09,722 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:10,915 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:10,916 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:10,916 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:10,916 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:10,916 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:10,916 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:10,918 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:10,918 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:10,918 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:10,918 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:10,945 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 16:47:11,047 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:12,169 - src.data.market_data_collector - WARNING - Trading pair not found: ETH/USDT on kraken
2025-08-07 16:47:12,507 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ETH/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:13,707 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:13,707 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:13,707 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:13,707 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:13,708 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:13,710 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:13,710 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:13,710 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:13,710 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:13,711 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:13,711 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:13,837 - src.exchanges.exchange_manager - ERROR - Failed to get ticker BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:14,532 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 16:47:15,293 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:15,876 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 16:47:16,511 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:16,512 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:16,513 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:16,513 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:16,514 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:16,514 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:16,514 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:16,515 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:16,515 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:16,515 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:16,515 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:16,676 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:17,085 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 16:47:18,133 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:18,418 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 16:47:19,336 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:19,336 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:19,336 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:19,336 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:19,336 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:19,336 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:19,336 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:19,336 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:19,336 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:19,336 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:19,336 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:19,474 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:19,604 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 16:47:20,874 - src.data.market_data_collector - WARNING - Trading pair not found: BNB/USDT on kraken
2025-08-07 16:47:20,940 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:23,090 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:23,090 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:23,091 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:23,091 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:23,091 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:23,091 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:23,091 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:23,092 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:23,092 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:23,092 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:23,092 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:23,092 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:23,093 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV BNB/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:23,188 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 16:47:24,261 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 16:47:24,556 - src.exchanges.exchange_manager - ERROR - Failed to get ticker XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:25,374 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 16:47:25,752 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:25,752 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:25,752 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:25,754 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:25,754 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:25,754 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:25,754 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:25,754 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:25,755 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:25,755 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:25,755 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:25,755 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:25,890 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:26,614 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 16:47:27,349 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:27,825 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 16:47:28,548 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:28,548 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:28,548 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:28,548 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:28,549 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:28,549 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:28,549 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:28,549 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:28,550 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:28,550 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:28,550 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:28,550 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:28,665 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:29,053 - src.data.market_data_collector - WARNING - Trading pair not found: XRP/USDT on kraken
2025-08-07 16:47:30,132 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:31,327 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:31,327 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:31,327 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:31,327 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:31,328 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:31,328 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:31,328 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:47:31,328 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:31,328 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:31,329 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:31,329 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:31,330 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:31,330 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:31,457 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 16:47:31,459 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:32,607 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 16:47:32,921 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV XRP/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:33,709 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 16:47:34,119 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:34,119 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:34,119 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:34,119 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:34,120 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:34,120 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:34,120 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:47:34,120 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:34,120 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:34,121 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:34,121 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:34,121 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:34,121 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:34,244 - src.exchanges.exchange_manager - ERROR - Failed to get ticker ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:35,048 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 16:47:35,735 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:36,339 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 16:47:36,907 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:36,908 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:36,908 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:36,908 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:36,908 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:36,909 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:36,909 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:47:36,909 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:36,909 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:36,909 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:36,910 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:36,910 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:36,910 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:37,045 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:37,528 - src.data.market_data_collector - WARNING - Trading pair not found: ADA/USDT on kraken
2025-08-07 16:47:38,494 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:39,693 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:39,693 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:39,694 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:39,694 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:39,694 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:39,695 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:39,695 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:47:39,695 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ADA/USDT, returning basic analysis
2025-08-07 16:47:39,695 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:39,695 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:39,696 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:39,696 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:39,696 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:39,696 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:39,747 - src.data.market_data_collector - WARNING - Trading pair not found: SOL/USDT on kraken
2025-08-07 16:47:39,819 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:40,958 - src.data.market_data_collector - WARNING - Trading pair not found: SOL/USDT on kraken
2025-08-07 16:47:41,280 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:42,137 - src.data.market_data_collector - WARNING - Trading pair not found: SOL/USDT on kraken
2025-08-07 16:47:42,481 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:42,482 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:42,482 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:42,482 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:42,482 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:42,483 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:42,483 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:47:42,483 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ADA/USDT, returning basic analysis
2025-08-07 16:47:42,483 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:42,483 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:42,484 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:42,484 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:42,484 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:42,484 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:42,606 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV ADA/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:43,405 - src.data.market_data_collector - WARNING - Trading pair not found: SOL/USDT on kraken
2025-08-07 16:47:44,072 - src.exchanges.exchange_manager - ERROR - Failed to get ticker SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:44,681 - src.data.market_data_collector - WARNING - Trading pair not found: SOL/USDT on kraken
2025-08-07 16:47:45,266 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:45,266 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:45,266 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:45,266 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:45,266 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:45,267 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:45,267 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:47:45,267 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ADA/USDT, returning basic analysis
2025-08-07 16:47:45,268 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:45,268 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:45,268 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:45,268 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:45,269 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:45,269 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:45,401 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:45,960 - src.data.market_data_collector - WARNING - Trading pair not found: SOL/USDT on kraken
2025-08-07 16:47:46,859 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:48,058 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:48,058 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:48,058 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:48,059 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:48,059 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:48,059 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:48,059 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:47:48,059 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ADA/USDT, returning basic analysis
2025-08-07 16:47:48,060 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for SOL/USDT, returning basic analysis
2025-08-07 16:47:48,060 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:48,060 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:48,060 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:48,060 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:48,060 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:48,061 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:48,185 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:48,363 - src.data.market_data_collector - WARNING - Trading pair not found: DOGE/USDT on kraken
2025-08-07 16:47:49,523 - src.data.market_data_collector - WARNING - Trading pair not found: DOGE/USDT on kraken
2025-08-07 16:47:49,663 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:50,819 - src.data.market_data_collector - WARNING - Trading pair not found: DOGE/USDT on kraken
2025-08-07 16:47:50,852 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:50,852 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:50,852 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:50,852 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:50,853 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:50,853 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:50,853 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:47:50,853 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ADA/USDT, returning basic analysis
2025-08-07 16:47:50,854 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for SOL/USDT, returning basic analysis
2025-08-07 16:47:50,854 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:50,854 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:50,854 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:50,855 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:50,855 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:50,855 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:50,968 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:52,041 - src.data.market_data_collector - WARNING - Trading pair not found: DOGE/USDT on kraken
2025-08-07 16:47:52,423 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV SOL/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:54,108 - src.data.market_data_collector - WARNING - Trading pair not found: DOGE/USDT on kraken
2025-08-07 16:47:54,368 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:54,369 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:54,369 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:54,369 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:54,369 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:54,369 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:54,370 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:47:54,370 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ADA/USDT, returning basic analysis
2025-08-07 16:47:54,370 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for SOL/USDT, returning basic analysis
2025-08-07 16:47:54,370 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:54,370 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:54,370 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:54,370 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:54,371 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:54,371 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:54,375 - src.exchanges.exchange_manager - ERROR - Failed to get ticker DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:55,214 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:55,428 - src.data.market_data_collector - WARNING - Trading pair not found: DOGE/USDT on kraken
2025-08-07 16:47:56,679 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:57,767 - src.data.market_data_collector - WARNING - Trading pair not found: DOT/USDT on kraken
2025-08-07 16:47:57,880 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:47:57,880 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:47:57,880 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:47:57,880 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:47:57,881 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:47:57,881 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:47:57,881 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:47:57,881 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ADA/USDT, returning basic analysis
2025-08-07 16:47:57,882 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for SOL/USDT, returning basic analysis
2025-08-07 16:47:57,882 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for DOGE/USDT, returning basic analysis
2025-08-07 16:47:57,882 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:57,883 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:57,883 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:57,883 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:57,883 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:47:57,883 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:47:58,008 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:47:58,978 - src.data.market_data_collector - WARNING - Trading pair not found: DOT/USDT on kraken
2025-08-07 16:47:59,464 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:48:00,205 - src.data.market_data_collector - WARNING - Trading pair not found: DOT/USDT on kraken
2025-08-07 16:48:00,660 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:48:00,660 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:48:00,660 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:48:00,660 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:48:00,661 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:48:00,661 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:48:00,661 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:48:00,661 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ADA/USDT, returning basic analysis
2025-08-07 16:48:00,661 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for SOL/USDT, returning basic analysis
2025-08-07 16:48:00,661 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for DOGE/USDT, returning basic analysis
2025-08-07 16:48:00,661 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:00,662 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:00,662 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:00,662 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:00,662 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:00,662 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:48:00,795 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:48:01,482 - src.data.market_data_collector - WARNING - Trading pair not found: DOT/USDT on kraken
2025-08-07 16:48:02,256 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOGE/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:48:02,847 - src.data.market_data_collector - WARNING - Trading pair not found: DOT/USDT on kraken
2025-08-07 16:48:03,455 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:48:03,455 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:48:03,455 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:48:03,457 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:48:03,457 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:48:03,457 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:48:03,457 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:48:03,457 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ADA/USDT, returning basic analysis
2025-08-07 16:48:03,457 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for SOL/USDT, returning basic analysis
2025-08-07 16:48:03,457 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for DOGE/USDT, returning basic analysis
2025-08-07 16:48:03,457 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:03,458 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:03,458 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:03,458 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:03,458 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:03,458 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:48:03,585 - src.exchanges.exchange_manager - ERROR - Failed to get ticker DOT/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:48:04,132 - src.data.market_data_collector - WARNING - Trading pair not found: DOT/USDT on kraken
2025-08-07 16:48:04,133 - src.exchanges.exchange_manager - ERROR - Failed to get ticker MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:48:04,134 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:48:04,134 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:48:04,134 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:48:04,134 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:48:04,135 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:48:04,135 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV MATIC/USDT from kraken: kraken does not have market symbol MATIC/USDT
2025-08-07 16:48:05,046 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOT/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:48:06,237 - src.exchanges.exchange_manager - WARNING - Health check failed for kraken: kraken fetchStatus() is not supported yet
2025-08-07 16:48:06,238 - src.risk.risk_manager - INFO - Risk limits check completed: healthy (1/1 passed)
2025-08-07 16:48:06,239 - src.portfolio.portfolio_manager - WARNING - Portfolio data not initialized
2025-08-07 16:48:06,239 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BTC/USDT, returning basic analysis
2025-08-07 16:48:06,239 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ETH/USDT, returning basic analysis
2025-08-07 16:48:06,239 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for BNB/USDT, returning basic analysis
2025-08-07 16:48:06,239 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for XRP/USDT, returning basic analysis
2025-08-07 16:48:06,241 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for ADA/USDT, returning basic analysis
2025-08-07 16:48:06,241 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for SOL/USDT, returning basic analysis
2025-08-07 16:48:06,241 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for DOGE/USDT, returning basic analysis
2025-08-07 16:48:06,242 - src.analysis.technical_analyzer - WARNING - No OHLCV data provided for DOT/USDT, returning basic analysis
2025-08-07 16:48:06,242 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:06,242 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:06,242 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:06,243 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:06,243 - src.core.trading_engine - ERROR - Error processing trading decision: 'str' object has no attribute 'get'
2025-08-07 16:48:06,243 - src.portfolio.portfolio_manager - WARNING - Portfolio not initialized, returning 0
2025-08-07 16:48:06,379 - src.exchanges.exchange_manager - ERROR - Failed to get OHLCV DOT/USDT from binance: binance {"code":-2008,"msg":"Invalid Api-Key ID."}
2025-08-07 16:48:06,594 - src.data.market_data_collector - WARNING - Trading pair not found: AVAX/USDT on kraken
