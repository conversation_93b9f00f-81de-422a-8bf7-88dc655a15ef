"""
Exchange Manager for AI Trading Bot System.

This module provides a unified interface for interacting with multiple
cryptocurrency exchanges using the ccxt library.

Author: inkbytefo
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from decimal import Decimal

import ccxt.async_support as ccxt
from ..config.settings import Settings
from ..config.security import SecurityManager
from ..monitoring.metrics import get_metrics_collector


class ExchangeManager:
    """
    Unified exchange manager for multiple cryptocurrency exchanges.
    
    Provides a consistent interface for trading operations across
    different exchanges using the ccxt library.
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.security_manager = SecurityManager(settings)
        self.metrics = get_metrics_collector()
        self.logger = logging.getLogger(__name__)
        
        # Exchange instances
        self.exchanges: Dict[str, ccxt.Exchange] = {}
        self.exchange_configs = {}
        
        # Rate limiting and connection management
        self.rate_limits = {}
        self.last_requests = {}
        
        # Supported exchanges
        self.supported_exchanges = {
            'binance': ccxt.binance,
            'kraken': ccxt.kraken
        }
    
    async def initialize(self):
        """Initialize exchange connections."""
        try:
            self.logger.info("Initializing Exchange Manager...")
            
            # Initialize configured exchanges
            await self._initialize_exchanges()
            
            # Test connections
            await self._test_connections()
            
            self.logger.info("✅ Exchange Manager initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Exchange Manager: {e}")
            raise
    
    async def _initialize_exchanges(self):
        """Initialize individual exchange connections."""
        exchange_settings = self.settings.exchanges
        
        # Binance
        if exchange_settings.binance_api_key and exchange_settings.binance_secret_key:
            await self._setup_binance()

        # Kraken
        if exchange_settings.kraken_api_key and exchange_settings.kraken_secret_key:
            await self._setup_kraken()
        
        if not self.exchanges:
            self.logger.warning("No exchange credentials configured")
        else:
            self.logger.info(f"Configured exchanges: {list(self.exchanges.keys())}")
    
    async def _setup_binance(self):
        """Setup Binance exchange connection."""
        try:
            config = {
                'apiKey': self.settings.exchanges.binance_api_key,
                'secret': self.settings.exchanges.binance_secret_key,
                'sandbox': self.settings.exchanges.binance_testnet,
                'enableRateLimit': True,
                'rateLimit': 1200,  # requests per minute
                'timeout': 30000,
                'options': {
                    'defaultType': 'spot',  # spot, margin, future, delivery
                }
            }
            
            exchange = ccxt.binance(config)
            self.exchanges['binance'] = exchange
            self.exchange_configs['binance'] = config
            
            self.logger.info("✅ Binance exchange configured")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to setup Binance: {e}")
    
    async def _setup_kraken(self):
        """Setup Kraken exchange connection."""
        try:
            config = {
                'apiKey': self.settings.exchanges.kraken_api_key,
                'secret': self.settings.exchanges.kraken_secret_key,
                'enableRateLimit': True,
                'rateLimit': 1000,  # 1 request per second
                'timeout': 30000,
            }
            
            exchange = ccxt.kraken(config)
            self.exchanges['kraken'] = exchange
            self.exchange_configs['kraken'] = config
            
            self.logger.info("✅ Kraken exchange configured")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to setup Kraken: {e}")
    
    async def _test_connections(self):
        """Test exchange connections."""
        for exchange_name, exchange in self.exchanges.items():
            try:
                # Test connection by fetching exchange status
                status = await exchange.fetch_status()
                
                if status.get('status') == 'ok':
                    self.logger.info(f"✅ {exchange_name} connection successful")
                else:
                    self.logger.warning(f"⚠️ {exchange_name} status: {status}")
                
            except Exception as e:
                self.logger.error(f"❌ {exchange_name} connection failed: {e}")
    
    async def get_markets(self, exchange_name: str) -> Dict[str, Any]:
        """Get available markets for an exchange."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            markets = await exchange.load_markets()
            
            self.logger.debug(f"Loaded {len(markets)} markets from {exchange_name}")
            return markets
            
        except Exception as e:
            self.logger.error(f"Failed to get markets from {exchange_name}: {e}")
            return {}
    
    async def get_ticker(self, exchange_name: str, symbol: str) -> Optional[Dict[str, Any]]:
        """Get ticker data for a symbol."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            ticker = await exchange.fetch_ticker(symbol)
            
            # Record metrics
            self.metrics.record_market_data_update(
                exchange_name, symbol, "ticker", 0.1
            )
            
            return ticker
            
        except Exception as e:
            self.logger.error(f"Failed to get ticker {symbol} from {exchange_name}: {e}")
            return None
    
    async def get_ohlcv(self, exchange_name: str, symbol: str, 
                       timeframe: str = '1m', limit: int = 100) -> List[List]:
        """Get OHLCV data for a symbol."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            # Record metrics
            self.metrics.record_market_data_update(
                exchange_name, symbol, timeframe, 0.1
            )
            
            return ohlcv
            
        except Exception as e:
            self.logger.error(f"Failed to get OHLCV {symbol} from {exchange_name}: {e}")
            return []
    
    async def get_order_book(self, exchange_name: str, symbol: str, 
                           limit: int = 100) -> Optional[Dict[str, Any]]:
        """Get order book data for a symbol."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            order_book = await exchange.fetch_order_book(symbol, limit)
            
            # Record metrics
            self.metrics.record_market_data_update(
                exchange_name, symbol, "orderbook", 0.1
            )
            
            return order_book
            
        except Exception as e:
            self.logger.error(f"Failed to get order book {symbol} from {exchange_name}: {e}")
            return None
    
    async def get_balance(self, exchange_name: str) -> Optional[Dict[str, Any]]:
        """Get account balance for an exchange."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            balance = await exchange.fetch_balance()
            
            return balance
            
        except Exception as e:
            self.logger.error(f"Failed to get balance from {exchange_name}: {e}")
            return None
    
    async def place_order(self, exchange_name: str, symbol: str, order_type: str,
                         side: str, amount: float, price: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """Place an order on an exchange."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            if self.settings.PAPER_TRADING:
                self.logger.info(f"📝 Paper trading: {side} {amount} {symbol} at {price}")
                return {
                    'id': f'paper_{datetime.utcnow().timestamp()}',
                    'symbol': symbol,
                    'type': order_type,
                    'side': side,
                    'amount': amount,
                    'price': price,
                    'status': 'closed',
                    'filled': amount,
                    'timestamp': datetime.utcnow().timestamp() * 1000
                }
            
            exchange = self.exchanges[exchange_name]
            
            # Place the order
            if order_type == 'market':
                order = await exchange.create_market_order(symbol, side, amount)
            else:
                order = await exchange.create_limit_order(symbol, side, amount, price)
            
            # Record metrics
            self.metrics.record_order(exchange_name, symbol, side, 'placed')
            
            self.logger.info(f"✅ Order placed: {order['id']}")
            return order
            
        except Exception as e:
            self.logger.error(f"Failed to place order on {exchange_name}: {e}")
            self.metrics.record_order(exchange_name, symbol, side, 'failed')
            return None
    
    async def get_order_status(self, exchange_name: str, order_id: str, 
                              symbol: str) -> Optional[Dict[str, Any]]:
        """Get order status."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            order = await exchange.fetch_order(order_id, symbol)
            
            return order
            
        except Exception as e:
            self.logger.error(f"Failed to get order status from {exchange_name}: {e}")
            return None
    
    async def cancel_order(self, exchange_name: str, order_id: str, 
                          symbol: str) -> bool:
        """Cancel an order."""
        try:
            if exchange_name not in self.exchanges:
                raise ValueError(f"Exchange {exchange_name} not configured")
            
            exchange = self.exchanges[exchange_name]
            await exchange.cancel_order(order_id, symbol)
            
            self.logger.info(f"✅ Order cancelled: {order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel order on {exchange_name}: {e}")
            return False
    
    async def health_check(self) -> bool:
        """Check health of all exchange connections."""
        try:
            healthy_exchanges = 0
            
            for exchange_name, exchange in self.exchanges.items():
                try:
                    status = await exchange.fetch_status()
                    if status.get('status') == 'ok':
                        healthy_exchanges += 1
                except Exception as e:
                    self.logger.warning(f"Health check failed for {exchange_name}: {e}")
            
            return healthy_exchanges > 0
            
        except Exception as e:
            self.logger.error(f"Exchange health check error: {e}")
            return False
    
    def get_supported_exchanges(self) -> List[str]:
        """Get list of supported exchanges."""
        return list(self.supported_exchanges.keys())
    
    def get_active_exchanges(self) -> List[str]:
        """Get list of active exchanges."""
        return list(self.exchanges.keys())
    
    async def close(self):
        """Close all exchange connections."""
        try:
            for exchange_name, exchange in self.exchanges.items():
                await exchange.close()
                self.logger.info(f"Closed connection to {exchange_name}")
                
        except Exception as e:
            self.logger.error(f"Error closing exchange connections: {e}")


# Global exchange manager instance
exchange_manager: Optional[ExchangeManager] = None


def get_exchange_manager() -> ExchangeManager:
    """Get the global exchange manager instance."""
    global exchange_manager
    if exchange_manager is None:
        from ..config.settings import Settings
        settings = Settings()
        exchange_manager = ExchangeManager(settings)
    return exchange_manager
